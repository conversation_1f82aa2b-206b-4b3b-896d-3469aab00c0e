import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[STRIPE-WEBHOOK] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    logStep("Webhook received");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    if (!webhookSecret) throw new Error("STRIPE_WEBHOOK_SECRET is not set");

    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });
    
    const body = await req.text();
    const signature = req.headers.get("stripe-signature");
    
    if (!signature) {
      throw new Error("No Stripe signature found");
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      logStep("Webhook signature verification failed", { error: err.message });
      return new Response(`Webhook Error: ${err.message}`, { status: 400 });
    }

    logStep("Event received", { type: event.type, id: event.id });

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session, supabaseClient);
        break;
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent, supabaseClient);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent, supabaseClient);
        break;
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object as Stripe.Dispute, supabaseClient);
        break;
      default:
        logStep("Unhandled event type", { type: event.type });
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session, supabaseClient: any) {
  logStep("Processing checkout session completed", { sessionId: session.id });
  
  const ticketId = session.metadata?.ticket_id;
  const userId = session.metadata?.user_id;
  
  if (!ticketId || !userId) {
    logStep("Missing metadata in session", { ticketId, userId });
    return;
  }

  // Update payment status
  const { error: paymentError } = await supabaseClient
    .from("payments")
    .update({ 
      status: "paid", 
      updated_at: new Date().toISOString(),
      stripe_payment_intent_id: session.payment_intent
    })
    .eq("stripe_session_id", session.id);

  if (paymentError) {
    logStep("Failed to update payment", { error: paymentError });
    throw paymentError;
  }

  // Update ticket status
  const { error: ticketError } = await supabaseClient
    .from("tickets")
    .update({ 
      payment_status: "paid",
      status: "open",
      updated_at: new Date().toISOString()
    })
    .eq("id", ticketId);

  if (ticketError) {
    logStep("Failed to update ticket", { error: ticketError });
    throw ticketError;
  }

  logStep("Successfully processed checkout session", { ticketId, userId });
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent, supabaseClient: any) {
  logStep("Processing payment succeeded", { paymentIntentId: paymentIntent.id });
  
  // Additional processing for successful payments
  const { error } = await supabaseClient
    .from("payments")
    .update({ 
      status: "paid",
      updated_at: new Date().toISOString()
    })
    .eq("stripe_payment_intent_id", paymentIntent.id);

  if (error) {
    logStep("Failed to update payment on success", { error });
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent, supabaseClient: any) {
  logStep("Processing payment failed", { paymentIntentId: paymentIntent.id });
  
  const { error } = await supabaseClient
    .from("payments")
    .update({ 
      status: "failed",
      updated_at: new Date().toISOString()
    })
    .eq("stripe_payment_intent_id", paymentIntent.id);

  if (error) {
    logStep("Failed to update payment on failure", { error });
  }
}

async function handleChargeDispute(dispute: Stripe.Dispute, supabaseClient: any) {
  logStep("Processing charge dispute", { disputeId: dispute.id, chargeId: dispute.charge });
  
  // Mark payment as disputed
  const { error } = await supabaseClient
    .from("payments")
    .update({ 
      status: "disputed",
      updated_at: new Date().toISOString()
    })
    .eq("stripe_charge_id", dispute.charge);

  if (error) {
    logStep("Failed to update payment on dispute", { error });
  }
}
