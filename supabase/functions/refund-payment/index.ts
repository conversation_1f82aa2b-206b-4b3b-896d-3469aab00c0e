import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[REFUND-PAYMENT] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    { auth: { persistSession: false } }
  );

  try {
    logStep("Refund request started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    const user = userData.user;
    if (!user) throw new Error("User not authenticated");

    // Check if user is admin
    const { data: userRole } = await supabaseClient
      .from("user_roles")
      .select("role")
      .eq("user_id", user.id)
      .single();

    if (!userRole || userRole.role !== "admin") {
      throw new Error("Unauthorized: Admin access required");
    }

    const { paymentId, reason, amount } = await req.json();
    if (!paymentId) throw new Error("Payment ID is required");

    logStep("Admin authenticated", { userId: user.id, paymentId });

    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });

    // Get payment details
    const { data: payment, error: paymentError } = await supabaseClient
      .from("payments")
      .select("*, tickets(*)")
      .eq("id", paymentId)
      .single();

    if (paymentError || !payment) {
      throw new Error("Payment not found");
    }

    if (payment.status !== "paid") {
      throw new Error("Can only refund paid payments");
    }

    // Create refund in Stripe
    const refundData: any = {
      payment_intent: payment.stripe_payment_intent_id,
      reason: reason || "requested_by_customer",
    };

    if (amount && amount < payment.amount) {
      refundData.amount = amount; // Partial refund
    }

    const refund = await stripe.refunds.create(refundData);

    // Update payment status
    const newStatus = amount && amount < payment.amount ? "partially_refunded" : "refunded";
    const { error: updateError } = await supabaseClient
      .from("payments")
      .update({ 
        status: newStatus,
        refund_amount: (payment.refund_amount || 0) + (amount || payment.amount),
        stripe_refund_id: refund.id,
        updated_at: new Date().toISOString()
      })
      .eq("id", paymentId);

    if (updateError) {
      logStep("Failed to update payment", { error: updateError });
      throw updateError;
    }

    // Update ticket status if fully refunded
    if (newStatus === "refunded") {
      await supabaseClient
        .from("tickets")
        .update({ 
          status: "refunded",
          payment_status: "refunded",
          updated_at: new Date().toISOString()
        })
        .eq("id", payment.ticket_id);
    }

    // Create refund record
    await supabaseClient
      .from("refunds")
      .insert({
        payment_id: paymentId,
        stripe_refund_id: refund.id,
        amount: amount || payment.amount,
        reason: reason || "requested_by_customer",
        processed_by: user.id,
        status: "completed"
      });

    logStep("Refund processed successfully", { 
      refundId: refund.id, 
      amount: amount || payment.amount 
    });

    return new Response(JSON.stringify({ 
      success: true,
      refundId: refund.id,
      amount: amount || payment.amount,
      status: newStatus
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logStep("ERROR", { message: errorMessage });
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
