-- Enhanced Ticket System Migration
-- This migration enhances the ticket system with categories, attachments, templates, and drafts

-- Add new columns to tickets table for enhanced functionality
ALTER TABLE tickets
ADD COLUMN IF NOT EXISTS category_id UUID,
ADD COLUMN IF NOT EXISTS urgency_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS estimated_resolution_time INTERVAL,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS system_info JSONB,
ADD COLUMN IF NOT EXISTS contact_method TEXT DEFAULT 'email',
ADD COLUMN IF NOT EXISTS preferred_contact_time TEXT;

-- Create ticket categories table
CREATE TABLE IF NOT EXISTS ticket_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    color TEXT DEFAULT '#10B981',
    default_priority TEXT DEFAULT 'medium',
    estimated_resolution_hours INTEGER DEFAULT 24,
    template_fields JSONB,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ticket attachments table
CREATE TABLE IF NOT EXISTS ticket_attachments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    file_url TEXT NOT NULL,
    storage_path TEXT NOT NULL,
    uploaded_by UUID NOT NULL,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ticket templates table
CREATE TABLE IF NOT EXISTS ticket_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID REFERENCES ticket_categories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    title_template TEXT,
    description_template TEXT,
    required_fields TEXT[],
    suggested_priority TEXT DEFAULT 'medium',
    is_active BOOLEAN DEFAULT true,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ticket drafts table for auto-save functionality
CREATE TABLE IF NOT EXISTS ticket_drafts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    title TEXT,
    description TEXT,
    category_id UUID REFERENCES ticket_categories(id) ON DELETE SET NULL,
    priority TEXT,
    system_info JSONB,
    form_data JSONB,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add new columns to payments table
ALTER TABLE payments
ADD COLUMN IF NOT EXISTS pricing_tier TEXT DEFAULT 'standard',
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_charge_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_refund_id TEXT,
ADD COLUMN IF NOT EXISTS refund_amount INTEGER DEFAULT 0;

-- Create refunds table
CREATE TABLE IF NOT EXISTS refunds (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    stripe_refund_id TEXT,
    amount INTEGER NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'completed',
    processed_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tickets_category_id ON tickets(category_id);
CREATE INDEX IF NOT EXISTS idx_tickets_urgency_level ON tickets(urgency_level);
CREATE INDEX IF NOT EXISTS idx_tickets_tags ON tickets USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_tickets_system_info ON tickets USING GIN(system_info);
CREATE INDEX IF NOT EXISTS idx_ticket_categories_is_active ON ticket_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_ticket_categories_sort_order ON ticket_categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_ticket_attachments_ticket_id ON ticket_attachments(ticket_id);
CREATE INDEX IF NOT EXISTS idx_ticket_attachments_uploaded_by ON ticket_attachments(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_ticket_templates_category_id ON ticket_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_ticket_templates_is_active ON ticket_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_ticket_drafts_user_id ON ticket_drafts(user_id);
CREATE INDEX IF NOT EXISTS idx_ticket_drafts_expires_at ON ticket_drafts(expires_at);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_customer_id ON payments(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payments_pricing_tier ON payments(pricing_tier);
CREATE INDEX IF NOT EXISTS idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX IF NOT EXISTS idx_refunds_stripe_refund_id ON refunds(stripe_refund_id);

-- Enable RLS for new tables
ALTER TABLE ticket_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_drafts ENABLE ROW LEVEL SECURITY;
ALTER TABLE refunds ENABLE ROW LEVEL SECURITY;

-- RLS policies for ticket_categories (public read, admin write)
CREATE POLICY "Anyone can view active ticket categories" ON ticket_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage ticket categories" ON ticket_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.users.id = auth.uid()
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- RLS policies for ticket_attachments
CREATE POLICY "Users can view attachments for their tickets" ON ticket_attachments
    FOR SELECT USING (
        uploaded_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM tickets
            WHERE tickets.id = ticket_attachments.ticket_id
            AND tickets.user_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.users.id = auth.uid()
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

CREATE POLICY "Users can upload attachments for their tickets" ON ticket_attachments
    FOR INSERT WITH CHECK (
        uploaded_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM tickets
            WHERE tickets.id = ticket_attachments.ticket_id
            AND tickets.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own attachments" ON ticket_attachments
    FOR DELETE USING (uploaded_by = auth.uid());

-- RLS policies for ticket_templates
CREATE POLICY "Anyone can view active ticket templates" ON ticket_templates
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage ticket templates" ON ticket_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.users.id = auth.uid()
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- RLS policies for ticket_drafts
CREATE POLICY "Users can manage their own drafts" ON ticket_drafts
    FOR ALL USING (user_id = auth.uid());

-- Policy: Users can view refunds for their own payments
CREATE POLICY "Users can view their own refunds" ON refunds
    FOR SELECT USING (
        payment_id IN (
            SELECT id FROM payments WHERE user_id = auth.uid()
        )
    );

-- Policy: Only admins can insert refunds
CREATE POLICY "Only admins can create refunds" ON refunds
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Policy: Only admins can update refunds
CREATE POLICY "Only admins can update refunds" ON refunds
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_roles 
            WHERE user_id = auth.uid() AND role = 'admin'
        )
    );

-- Update existing payments table RLS policies to include new fields
DROP POLICY IF EXISTS "Users can view their own payments" ON payments;
CREATE POLICY "Users can view their own payments" ON payments
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert their own payments" ON payments;
CREATE POLICY "Users can insert their own payments" ON payments
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Policy: Only system can update payments (via service role)
DROP POLICY IF EXISTS "System can update payments" ON payments;
CREATE POLICY "System can update payments" ON payments
    FOR UPDATE USING (true);

-- Insert default ticket categories
INSERT INTO ticket_categories (name, description, icon, color, default_priority, estimated_resolution_hours, sort_order) VALUES
('Technical Issue', 'Software bugs, system errors, and technical problems', 'Bug', '#EF4444', 'high', 4, 1),
('Account & Billing', 'Account access, billing questions, and subscription issues', 'CreditCard', '#F59E0B', 'medium', 2, 2),
('Feature Request', 'Suggestions for new features or improvements', 'Lightbulb', '#3B82F6', 'low', 72, 3),
('General Support', 'General questions and assistance', 'HelpCircle', '#10B981', 'medium', 24, 4),
('Security Concern', 'Security issues, vulnerabilities, and privacy concerns', 'Shield', '#DC2626', 'urgent', 1, 5),
('Integration Help', 'API integration, third-party services, and setup assistance', 'Plug', '#8B5CF6', 'medium', 8, 6)
ON CONFLICT (name) DO NOTHING;

-- Insert default ticket templates
INSERT INTO ticket_templates (category_id, name, description, title_template, description_template, required_fields, suggested_priority) VALUES
(
    (SELECT id FROM ticket_categories WHERE name = 'Technical Issue'),
    'Bug Report',
    'Template for reporting software bugs',
    'Bug: [Brief description]',
    'Steps to reproduce:
1.
2.
3.

Expected behavior:


Actual behavior:


Browser/Device information:


Additional context:',
    ARRAY['title', 'description', 'system_info'],
    'high'
),
(
    (SELECT id FROM ticket_categories WHERE name = 'Feature Request'),
    'Feature Request',
    'Template for requesting new features',
    'Feature Request: [Feature name]',
    'Problem this feature would solve:


Proposed solution:


Alternative solutions considered:


Additional context:',
    ARRAY['title', 'description'],
    'low'
);

-- Add updated_at trigger for new tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_ticket_categories_updated_at
    BEFORE UPDATE ON ticket_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ticket_templates_updated_at
    BEFORE UPDATE ON ticket_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ticket_drafts_updated_at
    BEFORE UPDATE ON ticket_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_refunds_updated_at
    BEFORE UPDATE ON refunds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Update payments table updated_at trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'update_payments_updated_at'
    ) THEN
        CREATE TRIGGER update_payments_updated_at
            BEFORE UPDATE ON payments
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create function to clean up expired drafts
CREATE OR REPLACE FUNCTION cleanup_expired_drafts()
RETURNS void AS $$
BEGIN
    DELETE FROM ticket_drafts WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-collect system information
CREATE OR REPLACE FUNCTION collect_system_info()
RETURNS jsonb AS $$
BEGIN
    RETURN jsonb_build_object(
        'timestamp', NOW(),
        'user_agent', current_setting('request.headers', true)::jsonb->>'user-agent',
        'ip_address', current_setting('request.headers', true)::jsonb->>'x-forwarded-for'
    );
END;
$$ LANGUAGE plpgsql;
