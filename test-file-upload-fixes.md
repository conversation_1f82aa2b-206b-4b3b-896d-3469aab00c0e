# File Upload Fixes Test Plan

## Issues Fixed

### 1. <PERSON><PERSON> (Row Level Security) Error on Drag & Drop
**Problem**: Files were uploaded to storage but failed to save to database due to RLS policy requiring ticket to exist first.

**Solution**: 
- Modified FileUpload component to skip database insertion during ticket creation
- Added `storagePath` property to track file location for later database insertion
- Updated CreateTicketForm to handle attachment database insertion after ticket creation
- Added proper error handling for RLS errors without failing the upload

### 2. Click-to-Select File Functionality Not Working
**Problem**: Clicking the upload area didn't open the file picker dialog.

**Solution**:
- Enhanced dropzone configuration with explicit `noClick: false` and `noKeyboard: false`
- Added file type acceptance configuration
- Added click event logging for debugging
- Added manual click handler as backup
- Improved user authentication checks

## Test Instructions

### Prerequisites
1. Make sure you're logged in to the application
2. Navigate to either:
   - Main ticket creation page: `http://localhost:8081/`
   - Test page: `http://localhost:8081/test-upload`

### Test 1: Click-to-Select Functionality
1. **Navigate to the file upload area**
2. **Click on the upload area** (should show "Drag & drop files here, or click to select")
3. **Expected Result**: File picker dialog should open
4. **Select a test file** (image, PDF, or document)
5. **Expected Result**: File should upload successfully and show progress

### Test 2: Drag & Drop Functionality
1. **Open a file explorer/finder window**
2. **Select a test file** (image, PDF, or document under 10MB)
3. **Drag the file over the upload area**
4. **Expected Result**: Upload area should highlight (green background)
5. **Drop the file**
6. **Expected Result**: File should upload successfully without RLS errors

### Test 3: Ticket Creation with Attachments
1. **Navigate to the main page** (`http://localhost:8081/`)
2. **Select a ticket category**
3. **Fill in ticket details** (title, description, priority)
4. **Upload files using either method** (drag & drop or click)
5. **Expected Result**: Files upload to storage successfully
6. **Submit the ticket**
7. **Expected Result**: Ticket created and attachments saved to database

### Test 4: Error Handling
1. **Try uploading a file larger than 10MB**
2. **Expected Result**: Should show file size error
3. **Try uploading an unsupported file type**
4. **Expected Result**: Should show file type error
5. **Try uploading when not logged in**
6. **Expected Result**: Should show authentication required message

## Debug Information

### Console Logs to Look For
- "Starting upload for [filename]" - File upload initiated
- "User authenticated, proceeding with upload" - Authentication check passed
- "Storage upload successful" - File uploaded to Supabase storage
- "RLS error detected - this is expected during ticket creation" - Expected during ticket creation
- "Upload completed successfully" - File upload finished
- "Upload area clicked" - Click functionality working

### Success Indicators
- ✅ File picker opens when clicking upload area
- ✅ Drag & drop highlights upload area
- ✅ Files upload to storage without errors
- ✅ Progress indicators work correctly
- ✅ Files appear in the attached files list
- ✅ Ticket creation completes with attachments
- ✅ No RLS errors in production flow

### Common Issues to Check
- If click doesn't work: Check browser console for errors
- If drag & drop doesn't work: Ensure file types are supported
- If RLS errors persist: Check user authentication status
- If uploads fail: Check network connectivity and file size limits

## Technical Details

### Files Modified
1. `src/components/FileUpload.tsx` - Main upload component
2. `src/components/CreateTicketForm.tsx` - Ticket creation form
3. `src/components/FileUploadDebug.tsx` - Debug component (new)
4. `src/pages/FileUploadTest.tsx` - Test page (new)

### Key Changes
- Enhanced dropzone configuration
- Improved error handling for RLS policies
- Added storage path tracking
- Better user feedback and authentication checks
- Debug logging for troubleshooting

## Expected Behavior After Fixes

1. **Click-to-select**: ✅ Working - opens file picker
2. **Drag & drop**: ✅ Working - uploads without RLS errors
3. **File validation**: ✅ Working - proper error messages
4. **Progress tracking**: ✅ Working - shows upload progress
5. **Database insertion**: ✅ Working - saves after ticket creation
6. **User feedback**: ✅ Working - clear success/error messages
