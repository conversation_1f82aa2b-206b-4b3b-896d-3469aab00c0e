export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      payments: {
        Row: {
          amount: number;
          created_at: string | null;
          currency: string | null;
          id: string;
          pricing_tier: string | null;
          refund_amount: number | null;
          status: string;
          stripe_charge_id: string | null;
          stripe_customer_id: string | null;
          stripe_payment_intent_id: string | null;
          stripe_refund_id: string | null;
          stripe_session_id: string | null;
          ticket_id: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          amount?: number;
          created_at?: string | null;
          currency?: string | null;
          id?: string;
          pricing_tier?: string | null;
          refund_amount?: number | null;
          status?: string;
          stripe_charge_id?: string | null;
          stripe_customer_id?: string | null;
          stripe_payment_intent_id?: string | null;
          stripe_refund_id?: string | null;
          stripe_session_id?: string | null;
          ticket_id: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          currency?: string | null;
          id?: string;
          pricing_tier?: string | null;
          refund_amount?: number | null;
          status?: string;
          stripe_charge_id?: string | null;
          stripe_customer_id?: string | null;
          stripe_payment_intent_id?: string | null;
          stripe_refund_id?: string | null;
          stripe_session_id?: string | null;
          ticket_id?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "payments_ticket_id_fkey";
            columns: ["ticket_id"];
            isOneToOne: false;
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          updated_at: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      refunds: {
        Row: {
          amount: number;
          created_at: string | null;
          id: string;
          payment_id: string;
          processed_by: string;
          reason: string | null;
          status: string;
          stripe_refund_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string | null;
          id?: string;
          payment_id: string;
          processed_by: string;
          reason?: string | null;
          status?: string;
          stripe_refund_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string | null;
          id?: string;
          payment_id?: string;
          processed_by?: string;
          reason?: string | null;
          status?: string;
          stripe_refund_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "refunds_payment_id_fkey";
            columns: ["payment_id"];
            isOneToOne: false;
            referencedRelation: "payments";
            referencedColumns: ["id"];
          }
        ];
      };
      ticket_attachments: {
        Row: {
          created_at: string | null;
          file_name: string;
          file_size: number;
          file_type: string;
          file_url: string;
          id: string;
          is_public: boolean | null;
          storage_path: string;
          ticket_id: string;
          uploaded_by: string;
        };
        Insert: {
          created_at?: string | null;
          file_name: string;
          file_size: number;
          file_type: string;
          file_url: string;
          id?: string;
          is_public?: boolean | null;
          storage_path: string;
          ticket_id: string;
          uploaded_by: string;
        };
        Update: {
          created_at?: string | null;
          file_name?: string;
          file_size?: number;
          file_type?: string;
          file_url?: string;
          id?: string;
          is_public?: boolean | null;
          storage_path?: string;
          ticket_id?: string;
          uploaded_by?: string;
        };
        Relationships: [
          {
            foreignKeyName: "ticket_attachments_ticket_id_fkey";
            columns: ["ticket_id"];
            isOneToOne: false;
            referencedRelation: "tickets";
            referencedColumns: ["id"];
          }
        ];
      };
      ticket_categories: {
        Row: {
          color: string | null;
          created_at: string | null;
          default_priority: string | null;
          description: string | null;
          estimated_resolution_hours: number | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          sort_order: number | null;
          template_fields: Json | null;
          updated_at: string | null;
        };
        Insert: {
          color?: string | null;
          created_at?: string | null;
          default_priority?: string | null;
          description?: string | null;
          estimated_resolution_hours?: number | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          sort_order?: number | null;
          template_fields?: Json | null;
          updated_at?: string | null;
        };
        Update: {
          color?: string | null;
          created_at?: string | null;
          default_priority?: string | null;
          description?: string | null;
          estimated_resolution_hours?: number | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          sort_order?: number | null;
          template_fields?: Json | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      ticket_drafts: {
        Row: {
          category_id: string | null;
          created_at: string | null;
          description: string | null;
          expires_at: string | null;
          form_data: Json | null;
          id: string;
          priority: string | null;
          system_info: Json | null;
          title: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string | null;
          description?: string | null;
          expires_at?: string | null;
          form_data?: Json | null;
          id?: string;
          priority?: string | null;
          system_info?: Json | null;
          title?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          category_id?: string | null;
          created_at?: string | null;
          description?: string | null;
          expires_at?: string | null;
          form_data?: Json | null;
          id?: string;
          priority?: string | null;
          system_info?: Json | null;
          title?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "ticket_drafts_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "ticket_categories";
            referencedColumns: ["id"];
          }
        ];
      };
      ticket_templates: {
        Row: {
          category_id: string | null;
          created_at: string | null;
          created_by: string | null;
          description: string | null;
          description_template: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          required_fields: string[] | null;
          suggested_priority: string | null;
          title_template: string | null;
          updated_at: string | null;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          description?: string | null;
          description_template?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          required_fields?: string[] | null;
          suggested_priority?: string | null;
          title_template?: string | null;
          updated_at?: string | null;
        };
        Update: {
          category_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          description?: string | null;
          description_template?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          required_fields?: string[] | null;
          suggested_priority?: string | null;
          title_template?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "ticket_templates_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "ticket_categories";
            referencedColumns: ["id"];
          }
        ];
      };
      tickets: {
        Row: {
          admin_response: string | null;
          category_id: string | null;
          contact_method: string | null;
          created_at: string | null;
          description: string;
          estimated_resolution_time: string | null;
          id: string;
          payment_session_id: string | null;
          payment_status: string;
          preferred_contact_time: string | null;
          priority: string;
          responded_at: string | null;
          responded_by: string | null;
          status: string;
          system_info: Json | null;
          tags: string[] | null;
          title: string;
          updated_at: string | null;
          urgency_level: number | null;
          user_id: string;
        };
        Insert: {
          admin_response?: string | null;
          category_id?: string | null;
          contact_method?: string | null;
          created_at?: string | null;
          description: string;
          estimated_resolution_time?: string | null;
          id?: string;
          payment_session_id?: string | null;
          payment_status?: string;
          preferred_contact_time?: string | null;
          priority?: string;
          responded_at?: string | null;
          responded_by?: string | null;
          status?: string;
          system_info?: Json | null;
          tags?: string[] | null;
          title: string;
          updated_at?: string | null;
          urgency_level?: number | null;
          user_id: string;
        };
        Update: {
          admin_response?: string | null;
          category_id?: string | null;
          contact_method?: string | null;
          created_at?: string | null;
          description?: string;
          estimated_resolution_time?: string | null;
          id?: string;
          payment_session_id?: string | null;
          payment_status?: string;
          preferred_contact_time?: string | null;
          priority?: string;
          responded_at?: string | null;
          responded_by?: string | null;
          status?: string;
          system_info?: Json | null;
          tags?: string[] | null;
          title?: string;
          updated_at?: string | null;
          urgency_level?: number | null;
          user_id?: string;
        };
        Relationships: [];
      };
      user_roles: {
        Row: {
          created_at: string | null;
          id: string;
          role: Database["public"]["Enums"]["app_role"];
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          role?: Database["public"]["Enums"]["app_role"];
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          role?: Database["public"]["Enums"]["app_role"];
          user_id?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      has_role: {
        Args: {
          _user_id: string;
          _role: Database["public"]["Enums"]["app_role"];
        };
        Returns: boolean;
      };
    };
    Enums: {
      app_role: "admin" | "user";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "user"],
    },
  },
} as const;
