// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nujwyiguqgrhrnvxndmd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51and5aWd1cWdyaHJudnhuZG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5Mzg0OTgsImV4cCI6MjA2NDUxNDQ5OH0.ojjUr_qYLfPXckk7CZ8PtM8QDdrNkNwgjJ6YTZP1FJw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);