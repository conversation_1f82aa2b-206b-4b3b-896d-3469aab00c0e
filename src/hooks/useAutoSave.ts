import { useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { validateDraft, DraftData } from '@/lib/validation';

interface UseAutoSaveOptions {
  delay?: number; // Auto-save delay in milliseconds
  onSave?: (success: boolean, error?: string) => void;
  onLoad?: (draft: DraftData | null) => void;
  enabled?: boolean;
}

interface UseAutoSaveReturn {
  saveDraft: (data: Partial<DraftData>) => Promise<boolean>;
  loadDraft: () => Promise<DraftData | null>;
  clearDraft: () => Promise<boolean>;
  hasDraft: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
}

export const useAutoSave = (
  formData: Partial<DraftData>,
  options: UseAutoSaveOptions = {}
): UseAutoSaveReturn => {
  const {
    delay = 3000, // 3 seconds default
    onSave,
    onLoad,
    enabled = true
  } = options;

  const { user } = useAuth();
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastDataRef = useRef<string>('');
  const isSavingRef = useRef(false);
  const lastSavedRef = useRef<Date | null>(null);
  const hasDraftRef = useRef(false);

  // Generate a unique draft key for the current user
  const getDraftKey = useCallback(() => {
    return user?.id ? `ticket_draft_${user.id}` : null;
  }, [user?.id]);

  // Save draft to database
  const saveDraft = useCallback(async (data: Partial<DraftData>): Promise<boolean> => {
    if (!user?.id || !enabled) return false;

    // Validate draft data
    const validation = validateDraft(data);
    if (!validation.success) {
      console.warn('Draft validation failed:', validation.errors);
      return false;
    }

    try {
      isSavingRef.current = true;

      // Check if draft already exists
      const { data: existingDraft } = await supabase
        .from('ticket_drafts')
        .select('id')
        .eq('user_id', user.id)
        .single();

      const draftData = {
        user_id: user.id,
        title: data.title || null,
        description: data.description || null,
        category_id: data.category_id || null,
        priority: data.priority || null,
        system_info: data.system_info || null,
        form_data: {
          contact_method: data.contact_method,
          preferred_contact_time: data.preferred_contact_time,
          tags: data.tags,
          urgency_level: data.urgency_level,
        },
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      };

      let error;
      if (existingDraft) {
        // Update existing draft
        const result = await supabase
          .from('ticket_drafts')
          .update(draftData)
          .eq('id', existingDraft.id);
        error = result.error;
      } else {
        // Create new draft
        const result = await supabase
          .from('ticket_drafts')
          .insert(draftData);
        error = result.error;
      }

      if (error) {
        console.error('Error saving draft:', error);
        onSave?.(false, error.message);
        return false;
      }

      lastSavedRef.current = new Date();
      hasDraftRef.current = true;
      onSave?.(true);
      return true;
    } catch (error: any) {
      console.error('Error saving draft:', error);
      onSave?.(false, error.message);
      return false;
    } finally {
      isSavingRef.current = false;
    }
  }, [user?.id, enabled, onSave]);

  // Load draft from database
  const loadDraft = useCallback(async (): Promise<DraftData | null> => {
    if (!user?.id || !enabled) return null;

    try {
      const { data: draft, error } = await supabase
        .from('ticket_drafts')
        .select('*')
        .eq('user_id', user.id)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !draft) {
        hasDraftRef.current = false;
        return null;
      }

      const draftData: DraftData = {
        title: draft.title,
        description: draft.description,
        category_id: draft.category_id,
        priority: draft.priority,
        system_info: draft.system_info,
        contact_method: draft.form_data?.contact_method,
        preferred_contact_time: draft.form_data?.preferred_contact_time,
        tags: draft.form_data?.tags,
        urgency_level: draft.form_data?.urgency_level,
      };

      hasDraftRef.current = true;
      onLoad?.(draftData);
      return draftData;
    } catch (error: any) {
      console.error('Error loading draft:', error);
      hasDraftRef.current = false;
      return null;
    }
  }, [user?.id, enabled, onLoad]);

  // Clear draft from database
  const clearDraft = useCallback(async (): Promise<boolean> => {
    if (!user?.id || !enabled) return false;

    try {
      const { error } = await supabase
        .from('ticket_drafts')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error clearing draft:', error);
        return false;
      }

      hasDraftRef.current = false;
      lastSavedRef.current = null;
      return true;
    } catch (error: any) {
      console.error('Error clearing draft:', error);
      return false;
    }
  }, [user?.id, enabled]);

  // Auto-save effect
  useEffect(() => {
    if (!enabled || !user?.id) return;

    // Convert form data to string for comparison
    const currentDataString = JSON.stringify(formData);

    // Only save if data has changed
    if (currentDataString !== lastDataRef.current && currentDataString !== '{}') {
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout for auto-save
      timeoutRef.current = setTimeout(() => {
        saveDraft(formData);
        lastDataRef.current = currentDataString;
      }, delay);
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [formData, delay, enabled, user?.id, saveDraft]);

  // Load draft on mount
  useEffect(() => {
    if (enabled && user?.id) {
      loadDraft();
    }
  }, [enabled, user?.id, loadDraft]);

  // Cleanup expired drafts periodically
  useEffect(() => {
    if (!enabled || !user?.id) return;

    const cleanupInterval = setInterval(async () => {
      try {
        await supabase
          .from('ticket_drafts')
          .delete()
          .lt('expires_at', new Date().toISOString());
      } catch (error) {
        console.error('Error cleaning up expired drafts:', error);
      }
    }, 60 * 60 * 1000); // Run every hour

    return () => clearInterval(cleanupInterval);
  }, [enabled, user?.id]);

  return {
    saveDraft,
    loadDraft,
    clearDraft,
    hasDraft: hasDraftRef.current,
    isSaving: isSavingRef.current,
    lastSaved: lastSavedRef.current,
  };
};
