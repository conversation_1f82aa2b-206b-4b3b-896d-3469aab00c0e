import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
  role?: 'admin' | 'user' | null;
  ticket_count?: number;
  payment_total?: number;
  last_login?: string;
}

export const useUsers = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'user'>('all');
  const { toast } = useToast();

  const loadUsers = async () => {
    try {
      setLoading(true);
      
      // Get all profiles with auth.users data
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) throw profilesError;

      // Get user roles
      const { data: roles, error: rolesError } = await supabase
        .from('user_roles')
        .select('user_id, role');

      if (rolesError) throw rolesError;

      // Get ticket counts for each user
      const { data: ticketCounts, error: ticketsError } = await supabase
        .from('tickets')
        .select('user_id')
        .then(({ data, error }) => {
          if (error) throw error;
          const counts: Record<string, number> = {};
          data?.forEach(ticket => {
            counts[ticket.user_id] = (counts[ticket.user_id] || 0) + 1;
          });
          return { data: counts, error: null };
        });

      if (ticketsError) throw ticketsError;

      // Get payment totals for each user
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select('user_id, amount, status')
        .eq('status', 'completed');

      if (paymentsError) throw paymentsError;

      const paymentTotals: Record<string, number> = {};
      payments?.forEach(payment => {
        paymentTotals[payment.user_id] = (paymentTotals[payment.user_id] || 0) + payment.amount;
      });

      // Combine all data
      const usersWithData: UserProfile[] = profiles?.map(profile => {
        const userRole = roles?.find(role => role.user_id === profile.id);
        return {
          ...profile,
          role: userRole?.role || null,
          ticket_count: ticketCounts.data?.[profile.id] || 0,
          payment_total: paymentTotals[profile.id] || 0,
        };
      }) || [];

      setUsers(usersWithData);
    } catch (error) {
      console.error('Error loading users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId: string, newRole: 'admin' | 'user' | null) => {
    try {
      if (newRole === null) {
        // Remove role
        const { error } = await supabase
          .from('user_roles')
          .delete()
          .eq('user_id', userId);
        
        if (error) throw error;
      } else {
        // Upsert role
        const { error } = await supabase
          .from('user_roles')
          .upsert({ user_id: userId, role: newRole }, { onConflict: 'user_id' });
        
        if (error) throw error;
      }

      toast({
        title: "Success",
        description: "User role updated successfully",
      });

      // Reload users to reflect changes
      await loadUsers();
    } catch (error) {
      console.error('Error updating user role:', error);
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    }
  };

  const updateUserProfile = async (userId: string, updates: Partial<UserProfile>) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: updates.full_name,
          email: updates.email,
          avatar_url: updates.avatar_url,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "User profile updated successfully",
      });

      // Reload users to reflect changes
      await loadUsers();
    } catch (error) {
      console.error('Error updating user profile:', error);
      toast({
        title: "Error",
        description: "Failed to update user profile",
        variant: "destructive",
      });
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      // Note: This will only delete the profile, not the auth user
      // In a real app, you'd need to use the Supabase Admin API to delete auth users
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "User profile deleted successfully",
      });

      // Reload users to reflect changes
      await loadUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      });
    }
  };

  // Filter users based on search term and role filter
  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchTerm || 
      user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesRole;
  });

  useEffect(() => {
    loadUsers();
  }, []);

  return {
    users: filteredUsers,
    loading,
    searchTerm,
    setSearchTerm,
    roleFilter,
    setRoleFilter,
    updateUserRole,
    updateUserProfile,
    deleteUser,
    refreshUsers: loadUsers,
  };
};
