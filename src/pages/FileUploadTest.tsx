import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import FileUploadDebug from '@/components/FileUploadDebug';

const FileUploadTest: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-4">Loading...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono">
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-retro-green mb-2">
            File Upload Test & Debug
          </h1>
          <p className="text-retro-green/70">
            Test both drag-and-drop and click-to-select functionality
          </p>
        </div>
        
        <FileUploadDebug />
      </div>
    </div>
  );
};

export default FileUploadTest;
