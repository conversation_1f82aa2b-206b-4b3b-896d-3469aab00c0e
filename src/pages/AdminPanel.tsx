import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useUserRole } from "@/hooks/useUserRole";
import ScanlineEffect from "@/components/ScanlineEffect";
import UserMenu from "@/components/UserMenu";
import PaymentManagement from "@/components/PaymentManagement";
import UserManagement from "@/components/UserManagement";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import SystemManagement from "@/components/SystemManagement";
import TerminalWindow from "@/components/TerminalWindow";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DollarSign,
  Users,
  Settings,
  BarChart3,
  Shield,
  Database,
  CreditCard,
} from "lucide-react";

const AdminPanel = () => {
  const { user } = useAuth();
  const { isAdmin } = useUserRole();
  const [activeTab, setActiveTab] = useState("payments");

  // Redirect non-admin users
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <TerminalWindow title="ACCESS_DENIED.EXE">
          <div className="text-center space-y-4">
            <Shield className="w-16 h-16 mx-auto text-red-500" />
            <div className="text-red-500 font-bold">ACCESS DENIED</div>
            <div className="text-retro-green/70">
              Administrator privileges required to access this area.
            </div>
          </div>
        </TerminalWindow>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />

      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">ADMIN_PANEL.EXE</div>
          <UserMenu />
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">
            &gt; ADMINISTRATION CONSOLE
          </h1>
          <p className="text-retro-green/80">
            Advanced system management and analytics
          </p>
        </div>

        {/* Admin Tabs */}
        <TerminalWindow title="ADMIN_MODULES.SYS">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-4 bg-black border border-retro-green">
              <TabsTrigger
                value="payments"
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                PAYMENTS
              </TabsTrigger>
              <TabsTrigger
                value="users"
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <Users className="w-4 h-4 mr-2" />
                USERS
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                ANALYTICS
              </TabsTrigger>
              <TabsTrigger
                value="system"
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <Settings className="w-4 h-4 mr-2" />
                SYSTEM
              </TabsTrigger>
            </TabsList>

            <TabsContent value="payments" className="mt-6">
              <PaymentManagement />
            </TabsContent>

            <TabsContent value="users" className="mt-6">
              <UserManagement />
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <AnalyticsDashboard />
            </TabsContent>

            <TabsContent value="system" className="mt-6">
              <SystemManagement />
            </TabsContent>
          </Tabs>
        </TerminalWindow>
      </div>
    </div>
  );
};

export default AdminPanel;
