import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import AuthForm from "@/components/AuthForm";
import ScanlineEffect from "@/components/ScanlineEffect";

const Auth = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (!loading && user) {
      navigate("/dashboard", { replace: true });
    }
  }, [user, loading, navigate]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-2">LOADING...</div>
          <div className="text-sm opacity-80">Checking authentication</div>
        </div>
      </div>
    );
  }

  // Don't render auth form if user is authenticated (will redirect)
  if (user) {
    return null;
  }

  return (
    <div className="relative">
      <AuthForm />
      <ScanlineEffect />
    </div>
  );
};

export default Auth;
