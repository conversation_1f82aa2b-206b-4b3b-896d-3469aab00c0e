
import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import CreateTicketForm from '@/components/CreateTicketForm';
import TicketList from '@/components/TicketList';
import ScanlineEffect from '@/components/ScanlineEffect';
import UserMenu from '@/components/UserMenu';

const Tickets = () => {
  const [refreshKey, setRefreshKey] = useState(0);
  const { user } = useAuth();
  const { isAdmin } = useUserRole();
  const { toast } = useToast();

  // Check for payment success
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session_id');
    
    if (sessionId) {
      // Verify payment status
      supabase.functions.invoke('verify-payment', {
        body: { sessionId }
      }).then(({ data, error }) => {
        if (error) {
          toast({
            title: "Payment Verification Failed",
            description: error.message,
            variant: "destructive",
          });
        } else if (data?.paymentStatus === 'paid') {
          toast({
            title: "Payment Successful!",
            description: "Your ticket has been submitted and will be reviewed by our team.",
          });
          setRefreshKey(prev => prev + 1);
          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);
        }
      });
    }
  }, [toast]);

  const handleTicketCreated = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />
      
      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">
            {isAdmin ? 'ADMIN_PANEL.EXE' : 'TICKET_SYSTEM.EXE'}
          </div>
          <UserMenu />
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">
            {isAdmin ? '&gt; ADMIN DASHBOARD' : '&gt; SUPPORT TICKETS'}
          </h1>
          <p className="text-retro-green/80">
            {isAdmin 
              ? 'Manage and respond to customer support requests'
              : 'Submit and track your support requests'
            }
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Create Ticket Form (Users Only) */}
          {!isAdmin && (
            <div>
              <CreateTicketForm onTicketCreated={handleTicketCreated} />
            </div>
          )}

          {/* Tickets List */}
          <div className={!isAdmin ? '' : 'lg:col-span-2'}>
            <TicketList key={refreshKey} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Tickets;
