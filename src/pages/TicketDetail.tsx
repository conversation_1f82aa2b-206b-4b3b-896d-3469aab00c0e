import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useUserRole } from "@/hooks/useUserRole";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import ScanlineEffect from "@/components/ScanlineEffect";
import UserMenu from "@/components/UserMenu";
import TerminalWindow from "@/components/TerminalWindow";
import {
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Calendar,
  User,
  Tag,
  FileText,
  Phone,
  Mail,
  Download,
} from "lucide-react";

interface TicketDetail {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  payment_status: string;
  admin_response?: string;
  created_at: string;
  updated_at: string;
  responded_at?: string;
  responded_by?: string;
  category_id?: string;
  urgency_level?: number;
  tags?: string[];
  contact_method?: string;
  preferred_contact_time?: string;
  system_info?: any;
  user_id: string;
  // Joined data
  profiles?: {
    email: string;
    full_name?: string;
  };
  ticket_categories?: {
    name: string;
    description: string;
    icon: string;
    color: string;
  };
  ticket_attachments?: Array<{
    id: string;
    file_name: string;
    file_size: number;
    file_type: string;
    file_url: string;
    created_at: string;
  }>;
}

const TicketDetail = () => {
  const { ticketId } = useParams<{ ticketId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isAdmin } = useUserRole();
  const { toast } = useToast();

  const [ticket, setTicket] = useState<TicketDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [adminResponse, setAdminResponse] = useState("");
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    if (ticketId) {
      loadTicketDetail();
    }
  }, [ticketId]);

  const loadTicketDetail = async () => {
    try {
      setLoading(true);

      // Build query based on user role
      let query = supabase
        .from("tickets")
        .select(
          `
          *,
          profiles:user_id (email, full_name),
          ticket_categories (name, description, icon, color),
          ticket_attachments (id, file_name, file_size, file_type, file_url, created_at)
        `
        )
        .eq("id", ticketId)
        .single();

      // Non-admin users can only see their own tickets
      if (!isAdmin) {
        query = query.eq("user_id", user?.id);
      }

      const { data, error } = await query;

      if (error) {
        if (error.code === "PGRST116") {
          toast({
            title: "Ticket Not Found",
            description:
              "The requested ticket does not exist or you don't have permission to view it.",
            variant: "destructive",
          });
          navigate("/tickets");
          return;
        }
        throw error;
      }

      setTicket(data);
      setAdminResponse(data.admin_response || "");
    } catch (error: any) {
      console.error("Error loading ticket:", error);
      toast({
        title: "Error",
        description: "Failed to load ticket details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateTicketStatus = async (ticketId: string, newStatus: string) => {
    setUpdating(true);
    try {
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString(),
      };

      if (adminResponse.trim()) {
        updateData.admin_response = adminResponse;
        updateData.responded_at = new Date().toISOString();
        updateData.responded_by = user?.id;
      }

      const { error } = await supabase
        .from("tickets")
        .update(updateData)
        .eq("id", ticketId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Ticket status updated to ${newStatus.replace("_", " ")}`,
      });

      // Reload ticket data
      loadTicketDetail();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update ticket",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending_payment":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "paid":
      case "in_progress":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "closed":
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-green-500";
      case "medium":
        return "bg-yellow-500";
      case "high":
        return "bg-orange-500";
      case "urgent":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-2">LOADING...</div>
          <div className="text-sm opacity-80">Fetching ticket details</div>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <TerminalWindow title="ERROR.LOG">
          <div className="text-center space-y-4">
            <XCircle className="w-16 h-16 mx-auto text-red-500" />
            <div className="text-red-500 font-bold">TICKET NOT FOUND</div>
            <div className="text-retro-green/70">
              The requested ticket could not be loaded.
            </div>
            <Button
              onClick={() => navigate("/tickets")}
              className="bg-retro-green text-black hover:bg-retro-green/90"
            >
              Return to Tickets
            </Button>
          </div>
        </TerminalWindow>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />

      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => navigate("/tickets")}
              variant="ghost"
              className="text-retro-green hover:text-retro-green/80 p-2"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div className="text-xl font-bold">TICKET_DETAIL.EXE</div>
          </div>
          <UserMenu />
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">
            &gt; TICKET #{ticket.id.slice(-8).toUpperCase()}
          </h1>
          <p className="text-retro-green/80">
            Detailed view and management interface
          </p>
        </div>

        {/* Ticket Details */}
        <TerminalWindow title="TICKET_INFORMATION.DAT">
          <div className="space-y-6">
            {/* Title and Status */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-retro-green">
                  {ticket.title}
                </h2>
                <div className="flex gap-2">
                  <Badge
                    className={`${getPriorityColor(
                      ticket.priority
                    )} text-black`}
                  >
                    {ticket.priority.toUpperCase()}
                  </Badge>
                  <Badge
                    variant="outline"
                    className="text-retro-green border-retro-green"
                  >
                    {ticket.status.replace("_", " ").toUpperCase()}
                  </Badge>
                  {ticket.payment_status && (
                    <Badge
                      variant="outline"
                      className={`${
                        ticket.payment_status === "paid"
                          ? "text-green-500 border-green-500"
                          : "text-yellow-500 border-yellow-500"
                      }`}
                    >
                      {ticket.payment_status.toUpperCase()}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Metadata Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-retro-green" />
                  <span className="text-sm text-retro-green/80">CREATED</span>
                </div>
                <div className="text-retro-green">
                  {new Date(ticket.created_at).toLocaleString()}
                </div>
              </div>

              <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-4 h-4 text-retro-green" />
                  <span className="text-sm text-retro-green/80">
                    SUBMITTED BY
                  </span>
                </div>
                <div className="text-retro-green">
                  {ticket.profiles?.full_name ||
                    ticket.profiles?.email ||
                    "Unknown User"}
                </div>
              </div>

              {ticket.urgency_level && (
                <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-retro-green" />
                    <span className="text-sm text-retro-green/80">
                      URGENCY LEVEL
                    </span>
                  </div>
                  <div className="text-retro-green">
                    {ticket.urgency_level}/10
                  </div>
                </div>
              )}

              {ticket.ticket_categories && (
                <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                  <div className="flex items-center gap-2 mb-2">
                    <Tag className="w-4 h-4 text-retro-green" />
                    <span className="text-sm text-retro-green/80">
                      CATEGORY
                    </span>
                  </div>
                  <div className="text-retro-green">
                    {ticket.ticket_categories.name}
                  </div>
                </div>
              )}

              {ticket.contact_method && (
                <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                  <div className="flex items-center gap-2 mb-2">
                    {ticket.contact_method === "email" ? (
                      <Mail className="w-4 h-4 text-retro-green" />
                    ) : (
                      <Phone className="w-4 h-4 text-retro-green" />
                    )}
                    <span className="text-sm text-retro-green/80">
                      CONTACT METHOD
                    </span>
                  </div>
                  <div className="text-retro-green">
                    {ticket.contact_method.toUpperCase()}
                  </div>
                </div>
              )}

              {ticket.preferred_contact_time && (
                <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-retro-green" />
                    <span className="text-sm text-retro-green/80">
                      PREFERRED TIME
                    </span>
                  </div>
                  <div className="text-retro-green">
                    {ticket.preferred_contact_time}
                  </div>
                </div>
              )}
            </div>

            {/* Description */}
            <div className="bg-black/50 p-4 rounded border border-retro-green/50">
              <div className="flex items-center gap-2 mb-3">
                <FileText className="w-4 h-4 text-retro-green" />
                <span className="text-sm text-retro-green/80">
                  ISSUE DESCRIPTION
                </span>
              </div>
              <div className="text-retro-green whitespace-pre-wrap">
                {ticket.description}
              </div>
            </div>

            {/* Tags */}
            {ticket.tags && ticket.tags.length > 0 && (
              <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                <div className="flex items-center gap-2 mb-3">
                  <Tag className="w-4 h-4 text-retro-green" />
                  <span className="text-sm text-retro-green/80">TAGS</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {ticket.tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-retro-green border-retro-green"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* System Information */}
            {ticket.system_info && (
              <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="w-4 h-4 text-retro-green" />
                  <span className="text-sm text-retro-green/80">
                    SYSTEM INFORMATION
                  </span>
                </div>
                <pre className="text-retro-green text-xs overflow-x-auto">
                  {JSON.stringify(ticket.system_info, null, 2)}
                </pre>
              </div>
            )}

            {/* Attachments */}
            {ticket.ticket_attachments &&
              ticket.ticket_attachments.length > 0 && (
                <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                  <div className="flex items-center gap-2 mb-3">
                    <Download className="w-4 h-4 text-retro-green" />
                    <span className="text-sm text-retro-green/80">
                      ATTACHMENTS
                    </span>
                  </div>
                  <div className="space-y-2">
                    {ticket.ticket_attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between p-2 border border-retro-green/30 rounded"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-retro-green" />
                          <span className="text-retro-green">
                            {attachment.file_name}
                          </span>
                          <span className="text-retro-green/60 text-xs">
                            ({formatFileSize(attachment.file_size)})
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() =>
                            window.open(attachment.file_url, "_blank")
                          }
                          className="text-retro-green hover:text-retro-green/80"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Admin Response */}
            {ticket.admin_response && (
              <div className="bg-retro-green/10 p-4 rounded border border-retro-green/50">
                <div className="flex items-center gap-2 mb-3">
                  <User className="w-4 h-4 text-retro-green" />
                  <span className="text-sm text-retro-green/80">
                    ADMIN RESPONSE
                  </span>
                  {ticket.responded_at && (
                    <span className="text-xs text-retro-green/60 ml-auto">
                      {new Date(ticket.responded_at).toLocaleString()}
                    </span>
                  )}
                </div>
                <div className="text-retro-green whitespace-pre-wrap">
                  {ticket.admin_response}
                </div>
              </div>
            )}
          </div>
        </TerminalWindow>

        {/* Admin Actions */}
        {isAdmin && ticket.payment_status === "paid" && (
          <TerminalWindow title="ADMIN_ACTIONS.EXE">
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-retro-green mb-2">
                  ADMIN RESPONSE:
                </label>
                <Textarea
                  value={adminResponse}
                  onChange={(e) => setAdminResponse(e.target.value)}
                  placeholder="Enter your response to the user..."
                  className="bg-black border-retro-green text-retro-green"
                  rows={4}
                />
              </div>

              <div className="flex gap-2 flex-wrap">
                <Button
                  onClick={() => updateTicketStatus(ticket.id, "in_progress")}
                  disabled={updating}
                  className="bg-orange-500 hover:bg-orange-600 text-black"
                >
                  Mark In Progress
                </Button>
                <Button
                  onClick={() => updateTicketStatus(ticket.id, "resolved")}
                  disabled={updating}
                  className="bg-green-500 hover:bg-green-600 text-black"
                >
                  Mark Resolved
                </Button>
                <Button
                  onClick={() => updateTicketStatus(ticket.id, "closed")}
                  disabled={updating}
                  className="bg-gray-500 hover:bg-gray-600 text-black"
                >
                  Close Ticket
                </Button>
              </div>
            </div>
          </TerminalWindow>
        )}
      </div>
    </div>
  );
};

export default TicketDetail;
