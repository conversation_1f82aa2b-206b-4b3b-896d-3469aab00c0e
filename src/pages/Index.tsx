
import TerminalWindow from '@/components/TerminalWindow';
import FeatureCard from '@/components/FeatureCard';
import ScanlineEffect from '@/components/ScanlineEffect';
import RetroButton from '@/components/RetroButton';
import UserMenu from '@/components/UserMenu';
import { ClipboardList, Bot, BarChart3 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />
      
      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">HELPDESK.EXE</div>
          <div className="flex items-center gap-6">
            <nav className="hidden md:flex gap-6">
              <button 
                onClick={() => navigate('/dashboard')}
                className="text-retro-green hover:text-retro-green/80 transition-colors"
              >
                DASHBOARD
              </button>
              <button 
                onClick={() => navigate('/tickets')}
                className="text-retro-green hover:text-retro-green/80 transition-colors"
              >
                TICKETS
              </button>
            </nav>
            <UserMenu />
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative z-10 py-20">
        <div className="max-w-4xl mx-auto text-center px-4">
          <TerminalWindow title="SYSTEM_STATUS.LOG">
            <div className="space-y-4">
              <div className="text-4xl font-bold mb-4">
                &gt; HELP DESK PROTOCOL INITIALIZED
              </div>
              <div className="text-lg opacity-80">
                Professional support tickets with integrated payment processing
              </div>
              <div className="text-sm mb-6">
                [STATUS] All systems operational. Ready for ticket processing.
              </div>
              <div className="flex gap-4 justify-center">
                <RetroButton onClick={() => navigate('/tickets')}>
                  CREATE_TICKET
                </RetroButton>
                <RetroButton 
                  variant="secondary"
                  onClick={() => navigate('/dashboard')}
                >
                  VIEW_DASHBOARD
                </RetroButton>
              </div>
            </div>
          </TerminalWindow>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 py-16">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-2xl font-bold text-center mb-12">
            &gt; CORE_FEATURES.EXE
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <FeatureCard
              title="TICKET_MGMT.SYS"
              description="Create support tickets with $29.99 fee. Track status from submission to resolution with real-time updates."
              icon={ClipboardList}
            />
            <FeatureCard
              title="PAYMENT_GATEWAY.BAT"
              description="Secure Stripe integration ensures tickets are only visible to admins after successful payment processing."
              icon={Bot}
            />
            <FeatureCard
              title="ADMIN_PANEL.DB"
              description="Role-based access with comprehensive admin tools for ticket management and revenue analytics."
              icon={BarChart3}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-16">
        <div className="max-w-4xl mx-auto text-center px-4">
          <TerminalWindow title="GET_STARTED.CFG">
            <div className="space-y-4">
              <div className="text-xl font-bold mb-4">
                &gt; READY_TO_GET_SUPPORT
              </div>
              <div className="text-sm space-y-2">
                <div>• Submit detailed support requests</div>
                <div>• Secure payment processing via Stripe</div>
                <div>• Real-time ticket status tracking</div>
                <div>• Priority support from our team</div>
              </div>
              <div className="mt-6">
                <RetroButton onClick={() => navigate('/tickets')}>
                  SUBMIT_TICKET_NOW
                </RetroButton>
              </div>
            </div>
          </TerminalWindow>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 border-t border-retro-green p-8 text-center text-sm opacity-60">
        <div>&copy; 2024 HELPDESK.EXE - Professional support system</div>
      </footer>
    </div>
  );
};

export default Index;
