import { z } from 'zod';

// Ticket form validation schema
export const ticketFormSchema = z.object({
  title: z
    .string()
    .min(5, 'Title must be at least 5 characters long')
    .max(200, 'Title must be less than 200 characters')
    .refine(
      (title) => title.trim().length > 0,
      'Title cannot be empty or just whitespace'
    ),
  
  description: z
    .string()
    .min(20, 'Description must be at least 20 characters long')
    .max(5000, 'Description must be less than 5000 characters')
    .refine(
      (desc) => desc.trim().length > 0,
      'Description cannot be empty or just whitespace'
    ),
  
  category_id: z
    .string()
    .uuid('Please select a valid category'),
  
  priority: z
    .enum(['low', 'medium', 'high', 'urgent'], {
      errorMap: () => ({ message: 'Please select a valid priority level' })
    }),
  
  contact_method: z
    .enum(['email', 'phone', 'both'], {
      errorMap: () => ({ message: 'Please select a preferred contact method' })
    })
    .optional()
    .default('email'),
  
  preferred_contact_time: z
    .string()
    .optional(),
  
  tags: z
    .array(z.string())
    .max(10, 'Maximum 10 tags allowed')
    .optional()
    .default([]),
  
  system_info: z
    .object({
      userAgent: z.string().optional(),
      platform: z.string().optional(),
      language: z.string().optional(),
      screenResolution: z.string().optional(),
      viewport: z.string().optional(),
      timezone: z.string().optional(),
      cookiesEnabled: z.boolean().optional(),
      onlineStatus: z.boolean().optional(),
      browserName: z.string().optional(),
      browserVersion: z.string().optional(),
      osName: z.string().optional(),
      deviceType: z.string().optional(),
      timestamp: z.string().optional(),
    })
    .optional(),
  
  urgency_level: z
    .number()
    .min(1, 'Urgency level must be at least 1')
    .max(5, 'Urgency level cannot exceed 5')
    .optional()
    .default(1),
});

// File upload validation
export const fileUploadSchema = z.object({
  name: z.string().min(1, 'File name is required'),
  size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
  type: z.string().refine(
    (type) => {
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'text/csv',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed'
      ];
      return allowedTypes.includes(type);
    },
    'File type not supported'
  )
});

// Draft validation (more lenient)
export const draftSchema = z.object({
  title: z.string().max(200, 'Title must be less than 200 characters').optional(),
  description: z.string().max(5000, 'Description must be less than 5000 characters').optional(),
  category_id: z.string().uuid().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  contact_method: z.enum(['email', 'phone', 'both']).optional(),
  preferred_contact_time: z.string().optional(),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').optional(),
  system_info: z.any().optional(),
  urgency_level: z.number().min(1).max(5).optional(),
});

// Validation helper functions
export const validateTicketForm = (data: any) => {
  try {
    return {
      success: true,
      data: ticketFormSchema.parse(data),
      errors: null
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: null,
        errors: error.errors.reduce((acc, err) => {
          const path = err.path.join('.');
          acc[path] = err.message;
          return acc;
        }, {} as Record<string, string>)
      };
    }
    return {
      success: false,
      data: null,
      errors: { general: 'Validation failed' }
    };
  }
};

export const validateFileUpload = (file: File) => {
  try {
    fileUploadSchema.parse({
      name: file.name,
      size: file.size,
      type: file.type
    });
    return { success: true, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors[0]?.message || 'File validation failed'
      };
    }
    return {
      success: false,
      error: 'File validation failed'
    };
  }
};

export const validateDraft = (data: any) => {
  try {
    return {
      success: true,
      data: draftSchema.parse(data),
      errors: null
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        data: null,
        errors: error.errors.reduce((acc, err) => {
          const path = err.path.join('.');
          acc[path] = err.message;
          return acc;
        }, {} as Record<string, string>)
      };
    }
    return {
      success: false,
      data: null,
      errors: { general: 'Draft validation failed' }
    };
  }
};

// Form field validation helpers
export const getFieldError = (errors: Record<string, string> | null, fieldName: string): string | undefined => {
  return errors?.[fieldName];
};

export const hasFieldError = (errors: Record<string, string> | null, fieldName: string): boolean => {
  return Boolean(errors?.[fieldName]);
};

// Real-time validation helpers
export const validateField = (fieldName: string, value: any, schema: z.ZodSchema) => {
  try {
    const fieldSchema = schema.shape[fieldName];
    if (fieldSchema) {
      fieldSchema.parse(value);
      return { isValid: true, error: null };
    }
    return { isValid: true, error: null };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.errors[0]?.message || 'Invalid value'
      };
    }
    return { isValid: false, error: 'Invalid value' };
  }
};

// Character count helpers
export const getCharacterCount = (text: string): number => {
  return text.length;
};

export const getWordCount = (text: string): number => {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
};

export const isFieldRequired = (fieldName: string): boolean => {
  const requiredFields = ['title', 'description', 'category_id', 'priority'];
  return requiredFields.includes(fieldName);
};

export type TicketFormData = z.infer<typeof ticketFormSchema>;
export type DraftData = z.infer<typeof draftSchema>;
export type FileUploadData = z.infer<typeof fileUploadSchema>;
