import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import TerminalWindow from './TerminalWindow';
import { Download, Receipt, CheckCircle } from 'lucide-react';

interface PaymentReceiptProps {
  payment: {
    id: string;
    amount: number;
    currency: string;
    status: string;
    pricing_tier: string;
    created_at: string;
    stripe_session_id: string;
    ticket_id: string;
  };
  ticket: {
    title: string;
    priority: string;
  };
  user: {
    email: string;
    full_name: string;
  };
}

const PaymentReceipt: React.FC<PaymentReceiptProps> = ({ payment, ticket, user }) => {
  const formatAmount = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTierDetails = (tier: string) => {
    const tiers = {
      standard: { name: 'Standard Support', responseTime: '24-48 hours' },
      priority: { name: 'Priority Support', responseTime: '4-12 hours' },
      urgent: { name: 'Urgent Support', responseTime: '< 1 hour' }
    };
    return tiers[tier] || tiers.standard;
  };

  const handleDownloadReceipt = () => {
    const receiptContent = `
RETRO HELP DESK - PAYMENT RECEIPT
================================

Receipt ID: ${payment.id}
Date: ${formatDate(payment.created_at)}
Status: ${payment.status.toUpperCase()}

CUSTOMER INFORMATION
-------------------
Name: ${user.full_name || 'N/A'}
Email: ${user.email}

SERVICE DETAILS
--------------
Service: ${getTierDetails(payment.pricing_tier).name}
Ticket: ${ticket.title}
Priority: ${ticket.priority.toUpperCase()}
Response Time: ${getTierDetails(payment.pricing_tier).responseTime}

PAYMENT DETAILS
--------------
Amount: ${formatAmount(payment.amount, payment.currency)}
Payment Method: Credit Card
Transaction ID: ${payment.stripe_session_id}

Thank you for choosing Retro Help Desk!
For support, contact <NAME_EMAIL>
    `;

    const blob = new Blob([receiptContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `receipt-${payment.id.slice(0, 8)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const tierDetails = getTierDetails(payment.pricing_tier);

  return (
    <TerminalWindow title="PAYMENT_RECEIPT.TXT">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center border-b border-retro-green/50 pb-4">
          <div className="flex items-center justify-center mb-2">
            <Receipt className="w-8 h-8 text-retro-green mr-2" />
            <h2 className="text-xl font-bold text-retro-green">PAYMENT RECEIPT</h2>
          </div>
          <div className="flex items-center justify-center">
            <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
            <Badge className="bg-green-500 text-black">PAYMENT SUCCESSFUL</Badge>
          </div>
        </div>

        {/* Receipt Details */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-4">
            <div>
              <h3 className="text-retro-green font-bold mb-2">TRANSACTION DETAILS</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Receipt ID:</span>
                  <span className="text-retro-green font-mono">{payment.id.slice(0, 8)}...</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Date:</span>
                  <span className="text-retro-green">{formatDate(payment.created_at)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Status:</span>
                  <Badge className="bg-green-500 text-black">{payment.status.toUpperCase()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Transaction ID:</span>
                  <span className="text-retro-green font-mono text-xs">{payment.stripe_session_id.slice(0, 12)}...</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-retro-green font-bold mb-2">CUSTOMER INFORMATION</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Name:</span>
                  <span className="text-retro-green">{user.full_name || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Email:</span>
                  <span className="text-retro-green">{user.email}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            <div>
              <h3 className="text-retro-green font-bold mb-2">SERVICE DETAILS</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Service:</span>
                  <span className="text-retro-green">{tierDetails.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Ticket:</span>
                  <span className="text-retro-green">{ticket.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Priority:</span>
                  <Badge variant="outline" className="border-retro-green text-retro-green">
                    {ticket.priority.toUpperCase()}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Response Time:</span>
                  <span className="text-retro-green">{tierDetails.responseTime}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-retro-green font-bold mb-2">PAYMENT SUMMARY</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Subtotal:</span>
                  <span className="text-retro-green">{formatAmount(payment.amount, payment.currency)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-retro-green/80">Tax:</span>
                  <span className="text-retro-green">$0.00</span>
                </div>
                <div className="border-t border-retro-green/50 pt-2">
                  <div className="flex justify-between font-bold">
                    <span className="text-retro-green">Total:</span>
                    <span className="text-retro-green text-lg">{formatAmount(payment.amount, payment.currency)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-retro-green/50 pt-4">
          <div className="flex justify-between items-center">
            <div className="text-xs text-retro-green/60">
              <p>Thank you for choosing Retro Help Desk!</p>
              <p>For support, contact <NAME_EMAIL></p>
            </div>
            <Button
              onClick={handleDownloadReceipt}
              variant="outline"
              size="sm"
              className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
            >
              <Download className="w-4 h-4 mr-2" />
              DOWNLOAD RECEIPT
            </Button>
          </div>
        </div>
      </div>
    </TerminalWindow>
  );
};

export default PaymentReceipt;
