
interface TerminalWindowProps {
  children: React.ReactNode;
  title?: string;
}

const TerminalWindow = ({ children, title = "RETRODESK.EXE" }: TerminalWindowProps) => {
  return (
    <div className="bg-retro-terminal border-2 border-retro-green shadow-lg">
      <div className="bg-retro-lightgray border-b border-retro-green px-4 py-2 flex items-center justify-between">
        <span className="font-mono text-retro-green text-sm font-bold">{title}</span>
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-retro-amber"></div>
          <div className="w-3 h-3 bg-retro-green"></div>
        </div>
      </div>
      <div className="p-6">
        {children}
      </div>
    </div>
  );
};

export default TerminalWindow;
