import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import FileUpload from './FileUpload';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadProgress?: number;
  error?: string;
  storagePath?: string;
}

const FileUploadDebug: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [testMode, setTestMode] = useState<'creation' | 'existing'>('creation');
  const { user } = useAuth();

  const handleFilesUploaded = (files: UploadedFile[]) => {
    console.log('Files uploaded in debug component:', files);
    setUploadedFiles(files);
  };

  const clearFiles = () => {
    setUploadedFiles([]);
  };

  const testClickFunctionality = () => {
    console.log('Testing click functionality...');
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      console.log('File input found, triggering click');
      fileInput.click();
    } else {
      console.error('File input not found');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="border-retro-green/50 bg-black">
        <CardHeader>
          <CardTitle className="text-retro-green font-mono">
            File Upload Debug & Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* User Info */}
          <div className="text-retro-green/80 text-sm space-y-1">
            <p>User: {user?.email || 'Not logged in'}</p>
            <p>User ID: {user?.id || 'N/A'}</p>
            <p>Test Mode: 
              <Badge className="ml-2" variant={testMode === 'creation' ? 'default' : 'secondary'}>
                {testMode === 'creation' ? 'Ticket Creation' : 'Existing Ticket'}
              </Badge>
            </p>
          </div>
          
          {/* Test Mode Toggle */}
          <div className="flex gap-2">
            <Button 
              onClick={() => setTestMode('creation')}
              variant={testMode === 'creation' ? 'default' : 'outline'}
              className="text-retro-green border-retro-green hover:bg-retro-green/10"
            >
              Test Creation Mode
            </Button>
            <Button 
              onClick={() => setTestMode('existing')}
              variant={testMode === 'existing' ? 'default' : 'outline'}
              className="text-retro-green border-retro-green hover:bg-retro-green/10"
            >
              Test Existing Ticket
            </Button>
          </div>
          
          {/* File Upload Component */}
          <div className="border border-retro-green/30 rounded-lg p-4">
            <h3 className="text-retro-green font-mono font-bold mb-4">
              File Upload Component:
            </h3>
            <FileUpload
              onFilesUploaded={handleFilesUploaded}
              maxFiles={3}
              maxFileSize={5}
              ticketId={testMode === 'existing' ? 'test-ticket-id' : undefined}
            />
          </div>
          
          {/* Manual Test Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={testClickFunctionality}
              variant="outline"
              className="text-retro-green border-retro-green hover:bg-retro-green/10"
            >
              Test Manual Click
            </Button>
            <Button 
              onClick={clearFiles}
              variant="outline"
              className="text-retro-green border-retro-green hover:bg-retro-green/10"
            >
              Clear Files
            </Button>
          </div>
          
          {/* Upload Results */}
          {uploadedFiles.length > 0 && (
            <div className="mt-6">
              <h3 className="text-retro-green font-mono font-bold mb-2">
                Upload Results:
              </h3>
              <div className="space-y-2">
                {uploadedFiles.map((file) => (
                  <div key={file.id} className="bg-black/50 p-3 rounded border border-retro-green/30">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-retro-green font-mono text-sm">{file.name}</p>
                        <p className="text-retro-green/60 text-xs">
                          Size: {(file.size / 1024 / 1024).toFixed(2)} MB | Type: {file.type}
                        </p>
                        {file.storagePath && (
                          <p className="text-retro-green/60 text-xs">
                            Storage: {file.storagePath}
                          </p>
                        )}
                        {file.url && (
                          <p className="text-retro-green/60 text-xs break-all">
                            URL: {file.url}
                          </p>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        {file.error ? (
                          <Badge variant="destructive" className="text-xs">
                            Error
                          </Badge>
                        ) : file.uploadProgress === 100 ? (
                          <Badge className="bg-retro-green text-black text-xs">
                            Success
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            {file.uploadProgress || 0}%
                          </Badge>
                        )}
                      </div>
                    </div>
                    {file.error && (
                      <p className="text-red-400 text-xs mt-2">Error: {file.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Debug JSON */}
          {uploadedFiles.length > 0 && (
            <div className="mt-4">
              <h3 className="text-retro-green font-mono font-bold mb-2">
                Debug JSON:
              </h3>
              <pre className="text-retro-green/80 text-xs bg-black/50 p-3 rounded border border-retro-green/30 overflow-auto max-h-40">
                {JSON.stringify(uploadedFiles, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FileUploadDebug;
