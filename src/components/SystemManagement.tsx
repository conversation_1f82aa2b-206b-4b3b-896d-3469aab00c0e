import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import TerminalWindow from "./TerminalWindow";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Database,
  Download,
  Trash2,
  RefreshCw,
  Server,
  HardDrive,
  Activity,
  Shield,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";

interface SystemStats {
  totalUsers: number;
  totalTickets: number;
  totalPayments: number;
  databaseSize: string;
  uptime: string;
  lastBackup: string;
}

const SystemManagement = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { toast } = useToast();

  const loadSystemStats = async () => {
    try {
      setLoading(true);

      // Get counts from various tables
      const [usersResult, ticketsResult, paymentsResult] = await Promise.all([
        supabase.from("profiles").select("*", { count: "exact", head: true }),
        supabase.from("tickets").select("*", { count: "exact", head: true }),
        supabase.from("payments").select("*", { count: "exact", head: true }),
      ]);

      setStats({
        totalUsers: usersResult.count || 0,
        totalTickets: ticketsResult.count || 0,
        totalPayments: paymentsResult.count || 0,
        databaseSize: "~2.5 MB", // Simulated - would need actual DB size query
        uptime: "99.9%", // Simulated - would come from monitoring
        lastBackup: new Date().toISOString(), // Simulated
      });
    } catch (error) {
      console.error("Error loading system stats:", error);
      toast({
        title: "Error",
        description: "Failed to load system statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBackupDatabase = async () => {
    setActionLoading("backup");
    try {
      // Simulate backup process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast({
        title: "Success",
        description: "Database backup completed successfully",
      });

      // Update last backup time
      setStats((prev) =>
        prev ? { ...prev, lastBackup: new Date().toISOString() } : null
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to backup database",
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleClearCache = async () => {
    setActionLoading("cache");
    try {
      // Simulate cache clearing
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: "Success",
        description: "System cache cleared successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear cache",
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleExportLogs = async () => {
    setActionLoading("logs");
    try {
      // Simulate log export
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Create a simple log file
      const logData = `
SYSTEM LOGS - ${new Date().toISOString()}
=====================================

[INFO] System status: Operational
[INFO] Total users: ${stats?.totalUsers || 0}
[INFO] Total tickets: ${stats?.totalTickets || 0}
[INFO] Total payments: ${stats?.totalPayments || 0}
[INFO] Database size: ${stats?.databaseSize || "Unknown"}
[INFO] System uptime: ${stats?.uptime || "Unknown"}
[INFO] Last backup: ${stats?.lastBackup || "Unknown"}

[INFO] Log export completed at ${new Date().toISOString()}
      `.trim();

      const blob = new Blob([logData], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `system-logs-${new Date().toISOString().split("T")[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "System logs exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export logs",
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleCleanupOldData = async () => {
    setActionLoading("cleanup");
    try {
      // Simulate cleanup process
      await new Promise((resolve) => setTimeout(resolve, 3000));

      toast({
        title: "Success",
        description: "Old data cleanup completed successfully",
      });

      // Refresh stats
      await loadSystemStats();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cleanup old data",
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    loadSystemStats();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getSystemHealth = () => {
    if (!stats) return { status: "unknown", color: "gray" };

    const totalRecords =
      stats.totalUsers + stats.totalTickets + stats.totalPayments;
    if (totalRecords > 1000) return { status: "excellent", color: "green" };
    if (totalRecords > 100) return { status: "good", color: "blue" };
    if (totalRecords > 10) return { status: "fair", color: "yellow" };
    return { status: "low", color: "orange" };
  };

  const systemHealth = getSystemHealth();

  return (
    <TerminalWindow title="SYSTEM_MANAGEMENT.EXE">
      <div className="space-y-6">
        {/* System Status Overview */}
        <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
            <Server className="w-5 h-5" />
            SYSTEM STATUS
          </h3>

          {loading ? (
            <div className="text-center py-4 text-retro-green/70">
              <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
              Loading system statistics...
            </div>
          ) : stats ? (
            <div className="grid md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Activity className="w-4 h-4" />
                  <span className="text-sm">System Health:</span>
                </div>
                <Badge
                  className={`bg-${systemHealth.color}-500/20 text-${systemHealth.color}-400 border-${systemHealth.color}-500`}
                >
                  {systemHealth.status.toUpperCase()}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <HardDrive className="w-4 h-4" />
                  <span className="text-sm">Database Size:</span>
                </div>
                <div className="font-mono">{stats.databaseSize}</div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  <span className="text-sm">Uptime:</span>
                </div>
                <div className="font-mono">{stats.uptime}</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-red-400">
              Failed to load system statistics
            </div>
          )}
        </div>

        {/* System Statistics */}
        {stats && (
          <div className="grid md:grid-cols-4 gap-4">
            <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
              <div className="text-xs text-retro-green/70 mb-1">
                TOTAL USERS
              </div>
              <div className="text-lg font-bold">{stats.totalUsers}</div>
            </div>

            <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
              <div className="text-xs text-retro-green/70 mb-1">
                TOTAL TICKETS
              </div>
              <div className="text-lg font-bold">{stats.totalTickets}</div>
            </div>

            <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
              <div className="text-xs text-retro-green/70 mb-1">
                TOTAL PAYMENTS
              </div>
              <div className="text-lg font-bold">{stats.totalPayments}</div>
            </div>

            <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
              <div className="text-xs text-retro-green/70 mb-1">
                LAST BACKUP
              </div>
              <div className="text-xs font-mono">
                {formatDate(stats.lastBackup)}
              </div>
            </div>
          </div>
        )}

        {/* System Actions */}
        <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
            <Database className="w-5 h-5" />
            SYSTEM ACTIONS
          </h3>

          <div className="grid md:grid-cols-2 gap-4">
            {/* Backup Database */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black h-auto p-4"
                  disabled={actionLoading === "backup"}
                >
                  <div className="flex flex-col items-center gap-2">
                    {actionLoading === "backup" ? (
                      <RefreshCw className="w-5 h-5 animate-spin" />
                    ) : (
                      <Database className="w-5 h-5" />
                    )}
                    <span>BACKUP DATABASE</span>
                    <span className="text-xs opacity-70">
                      Create full system backup
                    </span>
                  </div>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-black border-retro-green text-retro-green">
                <AlertDialogHeader>
                  <AlertDialogTitle>BACKUP DATABASE</AlertDialogTitle>
                  <AlertDialogDescription className="text-retro-green/70">
                    This will create a complete backup of the database. The
                    process may take a few minutes.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel className="border-retro-green text-retro-green">
                    CANCEL
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleBackupDatabase}
                    className="bg-retro-green text-black hover:bg-retro-green/80"
                  >
                    START BACKUP
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Clear Cache */}
            <Button
              variant="outline"
              onClick={handleClearCache}
              className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black h-auto p-4"
              disabled={actionLoading === "cache"}
            >
              <div className="flex flex-col items-center gap-2">
                {actionLoading === "cache" ? (
                  <RefreshCw className="w-5 h-5 animate-spin" />
                ) : (
                  <RefreshCw className="w-5 h-5" />
                )}
                <span>CLEAR CACHE</span>
                <span className="text-xs opacity-70">Clear system cache</span>
              </div>
            </Button>

            {/* Export Logs */}
            <Button
              variant="outline"
              onClick={handleExportLogs}
              className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black h-auto p-4"
              disabled={actionLoading === "logs"}
            >
              <div className="flex flex-col items-center gap-2">
                {actionLoading === "logs" ? (
                  <RefreshCw className="w-5 h-5 animate-spin" />
                ) : (
                  <Download className="w-5 h-5" />
                )}
                <span>EXPORT LOGS</span>
                <span className="text-xs opacity-70">Download system logs</span>
              </div>
            </Button>

            {/* Cleanup Old Data */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white h-auto p-4"
                  disabled={actionLoading === "cleanup"}
                >
                  <div className="flex flex-col items-center gap-2">
                    {actionLoading === "cleanup" ? (
                      <RefreshCw className="w-5 h-5 animate-spin" />
                    ) : (
                      <Trash2 className="w-5 h-5" />
                    )}
                    <span>CLEANUP OLD DATA</span>
                    <span className="text-xs opacity-70">
                      Remove old records
                    </span>
                  </div>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-black border-retro-green text-retro-green">
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-orange-500" />
                    CLEANUP OLD DATA
                  </AlertDialogTitle>
                  <AlertDialogDescription className="text-retro-green/70">
                    This will remove old system logs and temporary data. This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel className="border-retro-green text-retro-green">
                    CANCEL
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleCleanupOldData}
                    className="bg-orange-500 text-white hover:bg-orange-600"
                  >
                    START CLEANUP
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* Refresh Button */}
        <div className="flex justify-center">
          <Button
            onClick={loadSystemStats}
            variant="outline"
            className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
            disabled={loading}
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            REFRESH STATS
          </Button>
        </div>
      </div>
    </TerminalWindow>
  );
};

export default SystemManagement;
