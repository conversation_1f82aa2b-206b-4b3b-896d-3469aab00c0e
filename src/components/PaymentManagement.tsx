import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import TerminalWindow from "./TerminalWindow";
import { DollarSign, RefreshCw, AlertTriangle } from "lucide-react";

interface Payment {
  id: string;
  ticket_id: string;
  user_id: string;
  amount: number;
  currency: string;
  status: string;
  pricing_tier: string;
  stripe_session_id: string;
  stripe_customer_id: string;
  created_at: string;
  updated_at: string;
  tickets:
    | {
        title: string;
        priority: string;
      }
    | any;
  profiles:
    | {
        email: string;
        full_name: string;
      }
    | any;
}

const PaymentManagement = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [refundAmount, setRefundAmount] = useState("");
  const [refundReason, setRefundReason] = useState("");
  const [refundLoading, setRefundLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const loadPayments = async () => {
    try {
      // Use a raw SQL query to join the tables properly
      const { data, error } = await supabase.rpc("get_payments_with_user_info");

      if (error) {
        // Fallback to manual approach if RPC doesn't exist
        console.log("RPC not found, using fallback approach");

        const { data: paymentsData, error: paymentsError } = await supabase
          .from("payments")
          .select(
            `
            *,
            tickets (title, priority)
          `
          )
          .order("created_at", { ascending: false });

        if (paymentsError) throw paymentsError;

        // Get user profiles for each payment
        const paymentsWithProfiles = await Promise.all(
          (paymentsData || []).map(async (payment) => {
            const { data: profile } = await supabase
              .from("profiles")
              .select("email, full_name")
              .eq("id", payment.user_id)
              .single();

            return {
              ...payment,
              profiles: profile || {
                email: "Unknown",
                full_name: "Unknown User",
              },
            };
          })
        );

        setPayments(paymentsWithProfiles);
      } else {
        setPayments(data || []);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to load payments",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPayments();
  }, []);

  const handleRefund = async () => {
    if (!selectedPayment) return;

    setRefundLoading(true);
    try {
      const amount = refundAmount ? parseFloat(refundAmount) * 100 : undefined; // Convert to cents

      const { data, error } = await supabase.functions.invoke(
        "refund-payment",
        {
          body: {
            paymentId: selectedPayment.id,
            amount,
            reason: refundReason,
          },
        }
      );

      if (error) throw error;

      toast({
        title: "Refund Processed",
        description: `Refund of $${(
          (amount || selectedPayment.amount) / 100
        ).toFixed(2)} has been processed successfully.`,
      });

      setSelectedPayment(null);
      setRefundAmount("");
      setRefundReason("");
      loadPayments();
    } catch (error: any) {
      toast({
        title: "Refund Failed",
        description: error.message || "Failed to process refund",
        variant: "destructive",
      });
    } finally {
      setRefundLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-500", text: "PENDING" },
      paid: { color: "bg-green-500", text: "PAID" },
      failed: { color: "bg-red-500", text: "FAILED" },
      refunded: { color: "bg-gray-500", text: "REFUNDED" },
      partially_refunded: { color: "bg-orange-500", text: "PARTIAL REFUND" },
      disputed: { color: "bg-purple-500", text: "DISPUTED" },
    };

    const config = statusConfig[status] || {
      color: "bg-gray-500",
      text: status.toUpperCase(),
    };
    return (
      <Badge className={`${config.color} text-black`}>{config.text}</Badge>
    );
  };

  const formatAmount = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  if (loading) {
    return (
      <TerminalWindow title="PAYMENT_MANAGEMENT.EXE">
        <div className="text-retro-green">Loading payment data...</div>
      </TerminalWindow>
    );
  }

  return (
    <TerminalWindow title="PAYMENT_MANAGEMENT.EXE">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5 text-retro-green" />
            <span className="text-retro-green font-bold">PAYMENT RECORDS</span>
          </div>
          <Button
            onClick={loadPayments}
            variant="outline"
            size="sm"
            className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            REFRESH
          </Button>
        </div>

        <div className="border border-retro-green rounded">
          <Table>
            <TableHeader>
              <TableRow className="border-retro-green">
                <TableHead className="text-retro-green">ID</TableHead>
                <TableHead className="text-retro-green">Customer</TableHead>
                <TableHead className="text-retro-green">Ticket</TableHead>
                <TableHead className="text-retro-green">Amount</TableHead>
                <TableHead className="text-retro-green">Tier</TableHead>
                <TableHead className="text-retro-green">Status</TableHead>
                <TableHead className="text-retro-green">Date</TableHead>
                <TableHead className="text-retro-green">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id} className="border-retro-green/50">
                  <TableCell className="text-retro-green font-mono text-xs">
                    {payment.id.slice(0, 8)}...
                  </TableCell>
                  <TableCell className="text-retro-green">
                    <div>
                      <div className="font-semibold">
                        {payment.profiles?.full_name || "Unknown"}
                      </div>
                      <div className="text-xs text-retro-green/70">
                        {payment.profiles?.email || "Unknown"}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-retro-green">
                    <div>
                      <div className="font-semibold">
                        {payment.tickets?.title || "Unknown"}
                      </div>
                      <div className="text-xs text-retro-green/70">
                        {payment.tickets?.priority || "Unknown"}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-retro-green font-bold">
                    {formatAmount(payment.amount, payment.currency)}
                  </TableCell>
                  <TableCell className="text-retro-green">
                    {payment.pricing_tier?.toUpperCase() || "STANDARD"}
                  </TableCell>
                  <TableCell>{getStatusBadge(payment.status)}</TableCell>
                  <TableCell className="text-retro-green text-xs">
                    {new Date(payment.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {payment.status === "paid" && (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                            onClick={() => setSelectedPayment(payment)}
                          >
                            REFUND
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-black border-retro-green text-retro-green">
                          <DialogHeader>
                            <DialogTitle className="text-retro-green">
                              Process Refund
                            </DialogTitle>
                            <DialogDescription className="text-retro-green/70">
                              Process a refund for payment{" "}
                              {payment.id.slice(0, 8)}...
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label className="text-retro-green">
                                Refund Amount (leave empty for full refund)
                              </Label>
                              <Input
                                type="number"
                                step="0.01"
                                max={payment.amount / 100}
                                value={refundAmount}
                                onChange={(e) =>
                                  setRefundAmount(e.target.value)
                                }
                                placeholder={`Max: $${(
                                  payment.amount / 100
                                ).toFixed(2)}`}
                                className="bg-black border-retro-green text-retro-green"
                              />
                            </div>
                            <div>
                              <Label className="text-retro-green">Reason</Label>
                              <Textarea
                                value={refundReason}
                                onChange={(e) =>
                                  setRefundReason(e.target.value)
                                }
                                placeholder="Enter refund reason..."
                                className="bg-black border-retro-green text-retro-green"
                              />
                            </div>
                            <Button
                              onClick={handleRefund}
                              disabled={refundLoading}
                              className="w-full bg-red-500 hover:bg-red-600 text-white"
                            >
                              {refundLoading
                                ? "Processing..."
                                : "Process Refund"}
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {payments.length === 0 && (
          <div className="text-center text-retro-green/70 py-8">
            No payment records found.
          </div>
        )}
      </div>
    </TerminalWindow>
  );
};

export default PaymentManagement;
