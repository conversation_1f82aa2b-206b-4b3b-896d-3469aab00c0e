import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import RetroButton from "./RetroButton";

const AuthForm = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [loading, setLoading] = useState(false);
  const { signIn, signUp } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isLogin) {
        const { error } = await signIn(email, password);
        if (error) {
          toast({
            title: "Login Failed",
            description: error.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Welcome back!",
            description: "You have successfully logged in.",
          });
          // Redirect to dashboard after successful login
          navigate("/dashboard");
        }
      } else {
        const { error } = await signUp(email, password, fullName);
        if (error) {
          toast({
            title: "Signup Failed",
            description: error.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Account Created!",
            description: "Please check your email to verify your account.",
          });
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-black border border-retro-green p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold mb-2">HELPDESK.EXE</h1>
            <p className="text-sm opacity-80">
              {isLogin ? "LOGIN TO SYSTEM" : "CREATE NEW ACCOUNT"}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {!isLogin && (
              <div>
                <label className="block text-sm mb-2">FULL NAME:</label>
                <input
                  type="text"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full bg-black border border-retro-green p-3 text-retro-green focus:outline-none focus:border-retro-green focus:bg-gray-900"
                  required={!isLogin}
                  placeholder="Enter your full name"
                />
              </div>
            )}

            <div>
              <label className="block text-sm mb-2">EMAIL:</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-black border border-retro-green p-3 text-retro-green focus:outline-none focus:border-retro-green focus:bg-gray-900"
                required
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm mb-2">PASSWORD:</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full bg-black border border-retro-green p-3 text-retro-green focus:outline-none focus:border-retro-green focus:bg-gray-900"
                required
                placeholder="Enter password"
                minLength={6}
              />
            </div>

            <RetroButton type="submit" disabled={loading} className="w-full">
              {loading ? "PROCESSING..." : isLogin ? "LOGIN" : "CREATE ACCOUNT"}
            </RetroButton>
          </form>

          <div className="mt-6 text-center">
            <button
              type="button"
              onClick={() => setIsLogin(!isLogin)}
              className="text-sm text-retro-green hover:underline"
            >
              {isLogin
                ? "Need an account? Sign up"
                : "Already have an account? Login"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthForm;
