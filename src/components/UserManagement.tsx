import React, { useState } from "react";
import { useUsers, UserProfile } from "@/hooks/useUsers";
import UserEditDialog from "./UserEditDialog";
import CreateUserDialog from "./CreateUserDialog";
import TerminalWindow from "./TerminalWindow";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  User,
  Shield,
  Search,
  Edit,
  Trash2,
  RefreshCw,
  Users,
  DollarSign,
  Ticket,
} from "lucide-react";

const UserManagement = () => {
  const {
    users,
    loading,
    searchTerm,
    setSearchTerm,
    roleFilter,
    setRoleFilter,
    updateUserRole,
    updateUserProfile,
    deleteUser,
    refreshUsers,
  } = useUsers();

  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleDeleteUser = async (userId: string) => {
    await deleteUser(userId);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getRoleBadge = (role: string | null) => {
    if (role === "admin") {
      return (
        <Badge className="bg-red-500/20 text-red-400 border-red-500">
          <Shield className="w-3 h-3 mr-1" />
          ADMIN
        </Badge>
      );
    } else if (role === "user") {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500">
          <User className="w-3 h-3 mr-1" />
          USER
        </Badge>
      );
    } else {
      return (
        <Badge className="bg-gray-500/20 text-gray-400 border-gray-500">
          NO ROLE
        </Badge>
      );
    }
  };

  const totalUsers = users.length;
  const adminUsers = users.filter((u) => u.role === "admin").length;
  const regularUsers = users.filter((u) => u.role === "user").length;
  const totalRevenue = users.reduce(
    (sum, user) => sum + (user.payment_total || 0),
    0
  );

  return (
    <TerminalWindow title="USER_MANAGEMENT.EXE">
      <div className="space-y-6">
        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-1">
              <Users className="w-4 h-4" />
              <span className="text-xs text-retro-green/70">TOTAL USERS</span>
            </div>
            <div className="text-lg font-bold">{totalUsers}</div>
          </div>

          <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-1">
              <Shield className="w-4 h-4" />
              <span className="text-xs text-retro-green/70">ADMINS</span>
            </div>
            <div className="text-lg font-bold">{adminUsers}</div>
          </div>

          <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-1">
              <User className="w-4 h-4" />
              <span className="text-xs text-retro-green/70">REGULAR</span>
            </div>
            <div className="text-lg font-bold">{regularUsers}</div>
          </div>

          <div className="border border-retro-green/50 p-3 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-1">
              <DollarSign className="w-4 h-4" />
              <span className="text-xs text-retro-green/70">REVENUE</span>
            </div>
            <div className="text-lg font-bold">
              {formatCurrency(totalRevenue)}
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-retro-green/50" />
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-black border-retro-green text-retro-green"
            />
          </div>

          <Select
            value={roleFilter}
            onValueChange={(value: "all" | "admin" | "user") =>
              setRoleFilter(value)
            }
          >
            <SelectTrigger className="w-full md:w-48 bg-black border-retro-green text-retro-green">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent className="bg-black border-retro-green">
              <SelectItem value="all" className="text-retro-green">
                All Roles
              </SelectItem>
              <SelectItem value="admin" className="text-retro-green">
                Administrators
              </SelectItem>
              <SelectItem value="user" className="text-retro-green">
                Users
              </SelectItem>
            </SelectContent>
          </Select>

          <Button
            onClick={refreshUsers}
            variant="outline"
            className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            REFRESH
          </Button>

          <CreateUserDialog onUserCreated={refreshUsers} />
        </div>

        {/* Users Table */}
        <div className="border border-retro-green/50 rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center text-retro-green/70">
              <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
              Loading users...
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="border-retro-green/50">
                  <TableHead className="text-retro-green font-mono">
                    USER
                  </TableHead>
                  <TableHead className="text-retro-green font-mono">
                    ROLE
                  </TableHead>
                  <TableHead className="text-retro-green font-mono">
                    TICKETS
                  </TableHead>
                  <TableHead className="text-retro-green font-mono">
                    REVENUE
                  </TableHead>
                  <TableHead className="text-retro-green font-mono">
                    JOINED
                  </TableHead>
                  <TableHead className="text-retro-green font-mono">
                    ACTIONS
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id} className="border-retro-green/30">
                    <TableCell className="text-retro-green">
                      <div>
                        <div className="font-semibold">
                          {user.full_name || "Unknown"}
                        </div>
                        <div className="text-xs text-retro-green/70">
                          {user.email}
                        </div>
                        <div className="text-xs text-retro-green/50 font-mono">
                          ID: {user.id.slice(0, 8)}...
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getRoleBadge(user.role)}</TableCell>
                    <TableCell className="text-retro-green">
                      <div className="flex items-center gap-1">
                        <Ticket className="w-3 h-3" />
                        {user.ticket_count || 0}
                      </div>
                    </TableCell>
                    <TableCell className="text-retro-green font-mono">
                      {formatCurrency(user.payment_total || 0)}
                    </TableCell>
                    <TableCell className="text-retro-green text-xs">
                      {formatDate(user.created_at)}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditUser(user)}
                          className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
                        >
                          <Edit className="w-3 h-3" />
                        </Button>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="bg-black border-retro-green text-retro-green">
                            <AlertDialogHeader>
                              <AlertDialogTitle>DELETE USER</AlertDialogTitle>
                              <AlertDialogDescription className="text-retro-green/70">
                                Are you sure you want to delete this user? This
                                action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="border-retro-green text-retro-green">
                                CANCEL
                              </AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteUser(user.id)}
                                className="bg-red-500 text-white hover:bg-red-600"
                              >
                                DELETE
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>

        {users.length === 0 && !loading && (
          <div className="text-center py-8 text-retro-green/70">
            No users found matching your criteria.
          </div>
        )}
      </div>

      <UserEditDialog
        user={selectedUser}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSave={updateUserProfile}
        onRoleChange={updateUserRole}
      />
    </TerminalWindow>
  );
};

export default UserManagement;
