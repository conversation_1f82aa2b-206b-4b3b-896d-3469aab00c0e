
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import React from "react";

interface RetroButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "secondary";
  className?: string;
}

const RetroButton = ({ children, variant = "primary", className, ...props }: RetroButtonProps) => {
  return (
    <Button
      {...props}
      className={cn(
        "font-mono text-sm px-6 py-3 border-2 transition-all duration-200 hover:shadow-[0_0_10px_rgba(0,255,65,0.3)]",
        variant === "primary" && "bg-retro-terminal border-retro-green text-retro-green hover:bg-retro-green hover:text-retro-terminal",
        variant === "secondary" && "bg-transparent border-retro-amber text-retro-amber hover:bg-retro-amber hover:text-retro-terminal",
        className
      )}
    >
      {children}
    </Button>
  );
};

export default RetroButton;
