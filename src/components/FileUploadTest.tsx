import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import FileUpload from './FileUpload';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadProgress?: number;
  error?: string;
}

const FileUploadTest: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const { user } = useAuth();

  const handleFilesUploaded = (files: UploadedFile[]) => {
    console.log('Files uploaded in test component:', files);
    setUploadedFiles(files);
  };

  const clearFiles = () => {
    setUploadedFiles([]);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card className="border-retro-green/50 bg-black">
        <CardHeader>
          <CardTitle className="text-retro-green font-mono">
            File Upload Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-retro-green/80 text-sm">
            <p>User: {user?.email || 'Not logged in'}</p>
            <p>User ID: {user?.id || 'N/A'}</p>
          </div>
          
          <FileUpload
            onFilesUploaded={handleFilesUploaded}
            maxFiles={3}
            maxFileSize={5}
          />
          
          <div className="flex gap-2">
            <Button 
              onClick={clearFiles}
              variant="outline"
              className="text-retro-green border-retro-green hover:bg-retro-green/10"
            >
              Clear Files
            </Button>
          </div>
          
          {uploadedFiles.length > 0 && (
            <div className="mt-4">
              <h3 className="text-retro-green font-mono font-bold mb-2">
                Test Results:
              </h3>
              <pre className="text-retro-green/80 text-xs bg-black/50 p-2 rounded border border-retro-green/30 overflow-auto">
                {JSON.stringify(uploadedFiles, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FileUploadTest;
