import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUserRole } from "@/hooks/useUserRole";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import TerminalWindow from "./TerminalWindow";
import {
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  ExternalLink,
  Eye,
} from "lucide-react";

interface Ticket {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  payment_status: string;
  admin_response?: string;
  created_at: string;
  updated_at: string;
}

const TicketList: React.FC = () => {
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [adminResponse, setAdminResponse] = useState("");
  const [updating, setUpdating] = useState(false);
  const { user } = useAuth();
  const { isAdmin } = useUserRole();
  const { toast } = useToast();
  const navigate = useNavigate();

  const loadTickets = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("tickets")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setTickets(data || []);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to load tickets",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTickets();
  }, [user]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending_payment":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "paid":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "in_progress":
        return <Clock className="w-4 h-4 text-orange-500" />;
      case "resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "closed":
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500";
      case "high":
        return "bg-orange-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const updateTicketStatus = async (ticketId: string, newStatus: string) => {
    setUpdating(true);
    try {
      const { error } = await supabase
        .from("tickets")
        .update({
          status: newStatus,
          admin_response: adminResponse || null,
          responded_by: user?.id,
          responded_at: new Date().toISOString(),
        })
        .eq("id", ticketId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Ticket updated successfully",
      });

      loadTickets();
      setSelectedTicket(null);
      setAdminResponse("");
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update ticket",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <TerminalWindow title="LOADING_TICKETS.EXE">
        <div className="text-center text-retro-green">Loading tickets...</div>
      </TerminalWindow>
    );
  }

  const filteredTickets = isAdmin
    ? tickets.filter((t) => t.payment_status === "paid")
    : tickets;

  return (
    <div className="space-y-6">
      <TerminalWindow title={isAdmin ? "ADMIN_TICKETS.SYS" : "MY_TICKETS.LOG"}>
        <div className="space-y-4">
          {filteredTickets.length === 0 ? (
            <div className="text-center text-retro-green opacity-80">
              {isAdmin ? "No paid tickets to display" : "No tickets found"}
            </div>
          ) : (
            filteredTickets.map((ticket) => (
              <div
                key={ticket.id}
                className="group border border-retro-green p-4 rounded bg-retro-terminal/20 hover:bg-retro-terminal/40 hover:border-retro-green/80 cursor-pointer transition-all duration-200 hover:shadow-lg hover:shadow-retro-green/20 hover:scale-[1.02]"
                onClick={() => setSelectedTicket(ticket)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(ticket.status)}
                    <span className="font-mono font-bold text-retro-green group-hover:text-retro-green/90">
                      {ticket.title}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      className={`${getPriorityColor(
                        ticket.priority
                      )} text-black`}
                    >
                      {ticket.priority.toUpperCase()}
                    </Badge>
                    <Badge
                      variant="outline"
                      className="text-retro-green border-retro-green"
                    >
                      {ticket.status.replace("_", " ").toUpperCase()}
                    </Badge>
                    {/* Action buttons */}
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedTicket(ticket);
                        }}
                        className="text-retro-green hover:text-retro-green/80 p-1 h-6 w-6"
                        title="Quick view"
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/tickets/${ticket.id}`);
                        }}
                        className="text-retro-green hover:text-retro-green/80 p-1 h-6 w-6"
                        title="Open in detail page"
                      >
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="text-sm text-retro-green/80 mb-2">
                  {ticket.description.length > 100
                    ? `${ticket.description.substring(0, 100)}...`
                    : ticket.description}
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-retro-green/60">
                    Created: {new Date(ticket.created_at).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-retro-green/60 opacity-0 group-hover:opacity-100 transition-opacity">
                    Click to view details
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </TerminalWindow>

      {selectedTicket && (
        <TerminalWindow title="TICKET_DETAILS.VIEW">
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-retro-green">
                  {selectedTicket.title}
                </h3>
                <div className="flex gap-2">
                  <Badge
                    className={`${getPriorityColor(
                      selectedTicket.priority
                    )} text-black`}
                  >
                    {selectedTicket.priority.toUpperCase()}
                  </Badge>
                  <Badge
                    variant="outline"
                    className="text-retro-green border-retro-green"
                  >
                    {selectedTicket.status.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
              </div>

              <div className="bg-black/50 p-4 rounded border border-retro-green/50">
                <div className="text-sm text-retro-green/80 mb-2">
                  ISSUE DESCRIPTION:
                </div>
                <div className="text-retro-green">
                  {selectedTicket.description}
                </div>
              </div>

              {selectedTicket.admin_response && (
                <div className="bg-retro-green/10 p-4 rounded border border-retro-green/50 mt-4">
                  <div className="text-sm text-retro-green/80 mb-2">
                    ADMIN RESPONSE:
                  </div>
                  <div className="text-retro-green">
                    {selectedTicket.admin_response}
                  </div>
                </div>
              )}
            </div>

            {isAdmin && selectedTicket.payment_status === "paid" && (
              <div className="space-y-4 border-t border-retro-green/50 pt-4">
                <div>
                  <label className="block text-sm text-retro-green mb-2">
                    ADMIN RESPONSE:
                  </label>
                  <Textarea
                    value={adminResponse}
                    onChange={(e) => setAdminResponse(e.target.value)}
                    placeholder="Enter your response to the user..."
                    className="bg-black border-retro-green text-retro-green"
                    rows={4}
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() =>
                      updateTicketStatus(selectedTicket.id, "in_progress")
                    }
                    disabled={updating}
                    className="bg-orange-500 hover:bg-orange-600 text-black"
                  >
                    Mark In Progress
                  </Button>
                  <Button
                    onClick={() =>
                      updateTicketStatus(selectedTicket.id, "resolved")
                    }
                    disabled={updating}
                    className="bg-green-500 hover:bg-green-600 text-black"
                  >
                    Mark Resolved
                  </Button>
                  <Button
                    onClick={() =>
                      updateTicketStatus(selectedTicket.id, "closed")
                    }
                    disabled={updating}
                    className="bg-gray-500 hover:bg-gray-600 text-black"
                  >
                    Close Ticket
                  </Button>
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button
                onClick={() => navigate(`/tickets/${selectedTicket.id}`)}
                className="bg-retro-green text-black hover:bg-retro-green/90 flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Open Full Details
              </Button>
              <Button
                onClick={() => setSelectedTicket(null)}
                variant="outline"
                className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
              >
                Close
              </Button>
            </div>
          </div>
        </TerminalWindow>
      )}
    </div>
  );
};

export default TicketList;
