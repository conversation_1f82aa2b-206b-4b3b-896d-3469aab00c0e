
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

const FeatureCard = ({ icon: Icon, title, description }: FeatureCardProps) => {
  return (
    <div className="bg-retro-terminal border border-retro-lightgray p-6 hover:border-retro-green transition-colors duration-300">
      <div className="flex items-center mb-4">
        <Icon className="w-8 h-8 text-retro-green mr-3" />
        <h3 className="font-mono text-retro-green font-bold text-lg">{title}</h3>
      </div>
      <p className="font-mono text-gray-300 text-sm leading-relaxed">{description}</p>
    </div>
  );
};

export default FeatureCard;
