import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Bug, 
  CreditCard, 
  Lightbulb, 
  HelpCircle, 
  Shield, 
  Plug,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TicketCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  default_priority: string;
  estimated_resolution_hours: number;
  template_fields?: any;
  is_active: boolean;
  sort_order: number;
}

interface TicketCategorySelectorProps {
  selectedCategory: string | null;
  onCategorySelect: (category: TicketCategory | null) => void;
  className?: string;
}

const iconMap: Record<string, React.ReactNode> = {
  Bug: <Bug className="w-6 h-6" />,
  CreditCard: <CreditCard className="w-6 h-6" />,
  Lightbulb: <Lightbulb className="w-6 h-6" />,
  HelpCircle: <HelpCircle className="w-6 h-6" />,
  Shield: <Shield className="w-6 h-6" />,
  Plug: <Plug className="w-6 h-6" />,
};

const priorityColors: Record<string, string> = {
  low: 'bg-blue-500',
  medium: 'bg-yellow-500',
  high: 'bg-orange-500',
  urgent: 'bg-red-500',
};

const TicketCategorySelector: React.FC<TicketCategorySelectorProps> = ({
  selectedCategory,
  onCategorySelect,
  className
}) => {
  const [categories, setCategories] = useState<TicketCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('ticket_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setCategories(data || []);
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to load ticket categories",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatEstimatedTime = (hours: number) => {
    if (hours < 1) return '< 1 hour';
    if (hours === 1) return '1 hour';
    if (hours < 24) return `${hours} hours`;
    if (hours === 24) return '1 day';
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    if (remainingHours === 0) return `${days} day${days > 1 ? 's' : ''}`;
    return `${days} day${days > 1 ? 's' : ''} ${remainingHours}h`;
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-retro-green/60" />;
    }
  };

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="text-center">
          <h3 className="text-lg font-bold text-retro-green mb-2">SELECT ISSUE CATEGORY</h3>
          <p className="text-retro-green/70 text-sm">Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="text-center">
        <h3 className="text-lg font-bold text-retro-green mb-2">SELECT ISSUE CATEGORY</h3>
        <p className="text-retro-green/70 text-sm">
          Choose the category that best describes your issue for faster resolution
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category) => {
          const isSelected = selectedCategory === category.id;
          const icon = iconMap[category.icon] || <HelpCircle className="w-6 h-6" />;
          
          return (
            <Card
              key={category.id}
              className={cn(
                "relative cursor-pointer transition-all duration-200 hover:scale-105",
                isSelected
                  ? "border-retro-green bg-retro-green/10 shadow-lg shadow-retro-green/20"
                  : "border-retro-green/50 bg-black hover:border-retro-green hover:bg-retro-green/5"
              )}
              onClick={() => onCategorySelect(isSelected ? null : category)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div 
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: `${category.color}20`, color: category.color }}
                  >
                    {icon}
                  </div>
                  <Badge 
                    className={cn(
                      "text-xs text-white",
                      priorityColors[category.default_priority] || 'bg-gray-500'
                    )}
                  >
                    {category.default_priority.toUpperCase()}
                  </Badge>
                </div>
                <CardTitle className="text-retro-green text-lg font-mono">
                  {category.name}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="pt-0">
                <CardDescription className="text-retro-green/70 text-sm mb-3">
                  {category.description}
                </CardDescription>
                
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-1 text-retro-green/60">
                    {getPriorityIcon(category.default_priority)}
                    <span>Est. {formatEstimatedTime(category.estimated_resolution_hours)}</span>
                  </div>
                  
                  {isSelected && (
                    <Badge className="bg-retro-green text-black text-xs">
                      SELECTED
                    </Badge>
                  )}
                </div>
              </CardContent>
              
              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2 w-3 h-3 bg-retro-green rounded-full animate-pulse" />
              )}
            </Card>
          );
        })}
      </div>
      
      {selectedCategory && (
        <div className="text-center">
          <p className="text-retro-green/80 text-sm font-mono">
            Category selected! This will help us route your ticket to the right team.
          </p>
        </div>
      )}
    </div>
  );
};

export default TicketCategorySelector;
