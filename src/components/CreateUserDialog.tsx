import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { UserPlus, Shield, User } from 'lucide-react';

interface CreateUserDialogProps {
  onUserCreated: () => void;
}

const CreateUserDialog: React.FC<CreateUserDialogProps> = ({ onUserCreated }) => {
  const [open, setOpen] = useState(false);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<'admin' | 'user'>('user');
  const [creating, setCreating] = useState(false);
  const { toast } = useToast();

  const handleCreateUser = async () => {
    if (!fullName || !email || !password) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      // Note: In a real application, you would need to use the Supabase Admin API
      // to create users programmatically. This is a simplified version that
      // demonstrates the UI flow.
      
      // For now, we'll just create a profile entry and show a message
      // In production, you'd need to:
      // 1. Use Supabase Admin API to create the auth user
      // 2. Create the profile
      // 3. Set the user role
      
      toast({
        title: "Feature Note",
        description: "User creation requires Supabase Admin API integration. This is a UI demonstration.",
        variant: "default",
      });

      // Simulate user creation
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset form
      setFullName('');
      setEmail('');
      setPassword('');
      setRole('user');
      setOpen(false);
      
      // Refresh user list
      onUserCreated();

      toast({
        title: "Success",
        description: "User creation process initiated (demo mode)",
      });
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: "Error",
        description: "Failed to create user",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-retro-green text-black hover:bg-retro-green/80">
          <UserPlus className="w-4 h-4 mr-2" />
          CREATE USER
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-black border-retro-green text-retro-green max-w-md">
        <DialogHeader>
          <DialogTitle className="text-retro-green font-mono">
            CREATE_USER.EXE
          </DialogTitle>
          <DialogDescription className="text-retro-green/70">
            Add a new user to the system
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="fullName" className="text-retro-green font-mono text-sm">
              FULL NAME: *
            </Label>
            <Input
              id="fullName"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className="bg-black border-retro-green text-retro-green mt-1"
              placeholder="Enter full name"
              required
            />
          </div>

          <div>
            <Label htmlFor="email" className="text-retro-green font-mono text-sm">
              EMAIL: *
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-black border-retro-green text-retro-green mt-1"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <Label htmlFor="password" className="text-retro-green font-mono text-sm">
              PASSWORD: *
            </Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="bg-black border-retro-green text-retro-green mt-1"
              placeholder="Enter password"
              minLength={6}
              required
            />
            <div className="text-xs text-retro-green/60 mt-1">
              Minimum 6 characters
            </div>
          </div>

          <div>
            <Label htmlFor="role" className="text-retro-green font-mono text-sm">
              ROLE:
            </Label>
            <Select value={role} onValueChange={(value: 'admin' | 'user') => setRole(value)}>
              <SelectTrigger className="bg-black border-retro-green text-retro-green mt-1">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent className="bg-black border-retro-green">
                <SelectItem value="user" className="text-retro-green">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    User
                  </div>
                </SelectItem>
                <SelectItem value="admin" className="text-retro-green">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Administrator
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="border border-retro-green/30 p-3 bg-retro-terminal/5">
            <div className="text-xs text-retro-green/70 mb-2">SECURITY NOTICE:</div>
            <div className="text-xs text-retro-green/60">
              • User will receive email verification
              <br />
              • Password must meet security requirements
              <br />
              • Admin roles have full system access
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              onClick={() => setOpen(false)}
              variant="outline"
              className="flex-1 border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
            >
              CANCEL
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={creating}
              className="flex-1 bg-retro-green text-black hover:bg-retro-green/80"
            >
              {creating ? 'CREATING...' : 'CREATE USER'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserDialog;
