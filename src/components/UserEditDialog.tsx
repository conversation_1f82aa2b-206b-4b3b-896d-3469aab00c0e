import React, { useState, useEffect } from 'react';
import { UserProfile } from '@/hooks/useUsers';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { User, Shield, Mail, Calendar } from 'lucide-react';

interface UserEditDialogProps {
  user: UserProfile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (userId: string, updates: Partial<UserProfile>) => Promise<void>;
  onRoleChange: (userId: string, role: 'admin' | 'user' | null) => Promise<void>;
}

const UserEditDialog: React.FC<UserEditDialogProps> = ({
  user,
  open,
  onOpenChange,
  onSave,
  onRoleChange,
}) => {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'admin' | 'user' | 'none'>('none');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setFullName(user.full_name || '');
      setEmail(user.email || '');
      setRole(user.role || 'none');
    }
  }, [user]);

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile
      await onSave(user.id, {
        full_name: fullName,
        email: email,
      });

      // Update role if changed
      const newRole = role === 'none' ? null : role;
      if (newRole !== user.role) {
        await onRoleChange(user.id, newRole);
      }

      onOpenChange(false);
    } catch (error) {
      console.error('Error saving user:', error);
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-black border-retro-green text-retro-green max-w-md">
        <DialogHeader>
          <DialogTitle className="text-retro-green font-mono">
            EDIT_USER.EXE
          </DialogTitle>
          <DialogDescription className="text-retro-green/70">
            Modify user profile and permissions
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Info Header */}
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <div className="flex items-center gap-3 mb-3">
              <User className="w-5 h-5" />
              <span className="font-mono text-sm">USER_ID: {user.id.slice(0, 8)}...</span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <div className="text-retro-green/70">CREATED:</div>
                <div className="font-mono">{formatDate(user.created_at)}</div>
              </div>
              <div>
                <div className="text-retro-green/70">TICKETS:</div>
                <div className="font-mono">{user.ticket_count || 0}</div>
              </div>
            </div>
          </div>

          {/* Edit Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="fullName" className="text-retro-green font-mono text-sm">
                FULL NAME:
              </Label>
              <Input
                id="fullName"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="bg-black border-retro-green text-retro-green mt-1"
                placeholder="Enter full name"
              />
            </div>

            <div>
              <Label htmlFor="email" className="text-retro-green font-mono text-sm">
                EMAIL:
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-black border-retro-green text-retro-green mt-1"
                placeholder="Enter email address"
              />
            </div>

            <div>
              <Label htmlFor="role" className="text-retro-green font-mono text-sm">
                ROLE:
              </Label>
              <Select value={role} onValueChange={(value: 'admin' | 'user' | 'none') => setRole(value)}>
                <SelectTrigger className="bg-black border-retro-green text-retro-green mt-1">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent className="bg-black border-retro-green">
                  <SelectItem value="none" className="text-retro-green">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      No Role
                    </div>
                  </SelectItem>
                  <SelectItem value="user" className="text-retro-green">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      User
                    </div>
                  </SelectItem>
                  <SelectItem value="admin" className="text-retro-green">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      Administrator
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Current Role Badge */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-retro-green/70">CURRENT ROLE:</span>
            {user.role === 'admin' ? (
              <Badge className="bg-red-500/20 text-red-400 border-red-500">
                <Shield className="w-3 h-3 mr-1" />
                ADMIN
              </Badge>
            ) : user.role === 'user' ? (
              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500">
                <User className="w-3 h-3 mr-1" />
                USER
              </Badge>
            ) : (
              <Badge className="bg-gray-500/20 text-gray-400 border-gray-500">
                NO ROLE
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              onClick={() => onOpenChange(false)}
              variant="outline"
              className="flex-1 border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
            >
              CANCEL
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="flex-1 bg-retro-green text-black hover:bg-retro-green/80"
            >
              {saving ? 'SAVING...' : 'SAVE CHANGES'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserEditDialog;
