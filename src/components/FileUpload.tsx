import React, { useState, useCallback, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  X,
  File,
  Image,
  FileText,
  Archive,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface FileUploadProps {
  onFilesUploaded: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedFileTypes?: string[];
  ticketId?: string;
  className?: string;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadProgress?: number;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 5,
  maxFileSize = 10, // 10MB default
  acceptedFileTypes = [
    "image/*",
    "application/pdf",
    "text/*",
    ".doc,.docx,.xls,.xlsx,.ppt,.pptx",
    ".zip,.rar,.7z",
  ],
  ticketId,
  className,
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) return <Image className="w-4 h-4" />;
    if (fileType.includes("pdf") || fileType.includes("document"))
      return <FileText className="w-4 h-4" />;
    if (
      fileType.includes("zip") ||
      fileType.includes("rar") ||
      fileType.includes("archive")
    )
      return <Archive className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    console.log(`Validating file: ${file.name}`, {
      size: file.size,
      type: file.type,
      maxSize: maxFileSize * 1024 * 1024,
      acceptedTypes: acceptedFileTypes,
    });

    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      console.error(
        `File size validation failed for ${file.name}: ${file.size} > ${
          maxFileSize * 1024 * 1024
        }`
      );
      return `File size must be less than ${maxFileSize}MB`;
    }

    // Check file type - be more flexible with validation
    const isValidType = acceptedFileTypes.some((type) => {
      if (type.includes("*")) {
        const baseType = type.split("/")[0];
        const isMatch = file.type.startsWith(baseType);
        console.log(
          `Checking wildcard type ${type} against ${file.type}: ${isMatch}`
        );
        return isMatch;
      }

      // Check exact MIME type match
      if (file.type === type) {
        console.log(`Exact MIME type match: ${type}`);
        return true;
      }

      // Check file extension match for types that start with a dot
      if (type.startsWith(".")) {
        const extensions = type
          .split(",")
          .map((ext) => ext.trim().toLowerCase());
        const fileName = file.name.toLowerCase();
        const isMatch = extensions.some((ext) => fileName.endsWith(ext));
        console.log(
          `Checking extensions ${extensions} against ${fileName}: ${isMatch}`
        );
        return isMatch;
      }

      // Legacy check for file extension in type string
      const isExtMatch = file.name.toLowerCase().endsWith(type.toLowerCase());
      console.log(
        `Checking legacy extension ${type} against ${file.name}: ${isExtMatch}`
      );
      return isExtMatch;
    });

    if (!isValidType) {
      console.error(`File type validation failed for ${file.name}:`, {
        fileType: file.type,
        fileName: file.name,
        acceptedTypes: acceptedFileTypes,
      });
      return `File type not supported. Accepted types: ${acceptedFileTypes.join(
        ", "
      )}`;
    }

    console.log(`File validation passed for ${file.name}`);
    return null;
  };

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const fileName = `${fileId}-${file.name}`;
    const filePath = `ticket-attachments/${user?.id}/${fileName}`;

    console.log(`Starting upload for ${file.name}`, {
      fileId,
      filePath,
      fileSize: file.size,
      fileType: file.type,
    });

    // Create initial file object
    const uploadedFile: UploadedFile = {
      id: fileId,
      name: file.name,
      size: file.size,
      type: file.type,
      url: "",
      uploadProgress: 0,
    };

    try {
      // Check authentication before upload
      const {
        data: { user: currentUser },
        error: authError,
      } = await supabase.auth.getUser();
      if (authError || !currentUser) {
        console.error("Authentication check failed:", authError);
        throw new Error("User not authenticated");
      }

      console.log(
        `User authenticated, proceeding with upload for ${file.name}`
      );

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from("attachments")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) {
        console.error(`Storage upload failed for ${file.name}:`, error);
        throw error;
      }

      console.log(`Storage upload successful for ${file.name}:`, data);

      // Get public URL
      const {
        data: { publicUrl },
      } = supabase.storage.from("attachments").getPublicUrl(filePath);

      console.log(`Generated public URL for ${file.name}:`, publicUrl);

      uploadedFile.url = publicUrl;
      uploadedFile.uploadProgress = 100;

      // If ticketId is provided, save to database
      if (ticketId) {
        console.log(`Saving attachment to database for ticket ${ticketId}`);
        const { error: dbError } = await supabase
          .from("ticket_attachments")
          .insert({
            ticket_id: ticketId,
            file_name: file.name,
            file_size: file.size,
            file_type: file.type,
            file_url: publicUrl,
            storage_path: filePath,
            uploaded_by: user?.id,
          });

        if (dbError) {
          console.error("Error saving attachment to database:", dbError);
          // Don't throw here as the file is already uploaded
        } else {
          console.log(
            `Successfully saved attachment to database for ${file.name}`
          );
        }
      }

      console.log(`Upload completed successfully for ${file.name}`);
      return uploadedFile;
    } catch (error: any) {
      console.error(`Upload failed for ${file.name}:`, error);
      uploadedFile.error = error.message || "Upload failed";
      throw error;
    }
  };

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      console.log("Starting file upload process", {
        files: files.map((f) => ({ name: f.name, size: f.size, type: f.type })),
        user: user?.id,
      });

      if (!user) {
        console.error("Upload failed: User not authenticated");
        toast({
          title: "Authentication Required",
          description: "Please log in to upload files.",
          variant: "destructive",
        });
        return;
      }

      if (uploadedFiles.length + files.length > maxFiles) {
        console.error("Upload failed: Too many files", {
          current: uploadedFiles.length,
          adding: files.length,
          max: maxFiles,
        });
        toast({
          title: "Too Many Files",
          description: `Maximum ${maxFiles} files allowed.`,
          variant: "destructive",
        });
        return;
      }

      setUploading(true);
      const newFiles: UploadedFile[] = [];
      let successCount = 0;
      let errorCount = 0;

      for (const file of files) {
        console.log(`Processing file: ${file.name}`);

        const validationError = validateFile(file);
        if (validationError) {
          console.error(
            `File validation failed for ${file.name}:`,
            validationError
          );
          toast({
            title: "Invalid File",
            description: `${file.name}: ${validationError}`,
            variant: "destructive",
          });
          errorCount++;
          continue;
        }

        try {
          console.log(`Uploading file: ${file.name}`);
          const uploadedFile = await uploadFile(file);
          newFiles.push(uploadedFile);
          successCount++;
          console.log(`Successfully uploaded: ${file.name}`);
        } catch (error: any) {
          console.error(`Upload failed for ${file.name}:`, error);
          errorCount++;
          toast({
            title: "Upload Failed",
            description: `Failed to upload ${file.name}: ${error.message}`,
            variant: "destructive",
          });
        }
      }

      const allFiles = [...uploadedFiles, ...newFiles];
      setUploadedFiles(allFiles);
      onFilesUploaded(allFiles);
      setUploading(false);

      // Show summary toast
      if (successCount > 0) {
        toast({
          title: "Upload Complete",
          description: `Successfully uploaded ${successCount} file${
            successCount > 1 ? "s" : ""
          }${errorCount > 0 ? `, ${errorCount} failed` : ""}`,
          variant:
            successCount > 0 && errorCount === 0 ? "default" : "destructive",
        });
      }

      console.log("File upload process completed", {
        successCount,
        errorCount,
        totalFiles: allFiles.length,
      });
    },
    [user, maxFiles, uploadedFiles, onFilesUploaded, toast, uploadFile]
  );

  const removeFile = async (fileId: string) => {
    const fileToRemove = uploadedFiles.find((f) => f.id === fileId);
    if (!fileToRemove) return;

    try {
      // Remove from storage if URL exists
      if (fileToRemove.url) {
        const pathMatch = fileToRemove.url.match(/ticket-attachments\/.*$/);
        if (pathMatch) {
          await supabase.storage.from("attachments").remove([pathMatch[0]]);
        }
      }

      // Remove from database if ticketId exists
      if (ticketId) {
        await supabase
          .from("ticket_attachments")
          .delete()
          .eq("ticket_id", ticketId)
          .eq("file_name", fileToRemove.name);
      }

      const updatedFiles = uploadedFiles.filter((f) => f.id !== fileId);
      setUploadedFiles(updatedFiles);
      onFilesUploaded(updatedFiles);
    } catch (error: any) {
      toast({
        title: "Error",
        description: `Failed to remove file: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      handleFileUpload(acceptedFiles);
    },
    [handleFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles: maxFiles - uploadedFiles.length,
    disabled: uploading || uploadedFiles.length >= maxFiles,
  });

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
          isDragActive
            ? "border-retro-green bg-retro-green/10"
            : "border-retro-green/50 hover:border-retro-green hover:bg-retro-green/5",
          (uploading || uploadedFiles.length >= maxFiles) &&
            "opacity-50 cursor-not-allowed"
        )}
      >
        <input {...getInputProps()} ref={fileInputRef} />
        <Upload className="w-8 h-8 mx-auto mb-2 text-retro-green" />
        <p className="text-retro-green font-mono">
          {isDragActive
            ? "Drop files here..."
            : `Drag & drop files here, or click to select`}
        </p>
        <p className="text-retro-green/60 text-sm mt-1">
          Max {maxFiles} files, {maxFileSize}MB each
        </p>
        <p className="text-retro-green/60 text-xs mt-1">
          Supported: Images, PDFs, Documents, Archives
        </p>
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-retro-green font-mono font-bold">
            Attached Files:
          </h4>
          {uploadedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-3 border border-retro-green/50 rounded bg-black/50"
            >
              <div className="flex items-center space-x-3 flex-1">
                {getFileIcon(file.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-retro-green font-mono text-sm truncate">
                    {file.name}
                  </p>
                  <p className="text-retro-green/60 text-xs">
                    {formatFileSize(file.size)}
                  </p>
                </div>
                {file.error ? (
                  <Badge variant="destructive" className="text-xs">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Error
                  </Badge>
                ) : file.uploadProgress === 100 ? (
                  <Badge className="bg-retro-green text-black text-xs">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Uploaded
                  </Badge>
                ) : (
                  <div className="w-16">
                    <Progress
                      value={file.uploadProgress || 0}
                      className="h-2"
                    />
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(file.id)}
                className="text-retro-green hover:text-red-400 ml-2"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
