import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import TerminalWindow from './TerminalWindow';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  BarChart3,
  TrendingUp,
  Users,
  Ticket,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
} from 'lucide-react';

interface AnalyticsData {
  totalUsers: number;
  totalTickets: number;
  totalRevenue: number;
  avgResponseTime: number;
  ticketsByStatus: Record<string, number>;
  ticketsByPriority: Record<string, number>;
  revenueByMonth: Array<{ month: string; revenue: number }>;
  topUsers: Array<{ id: string; name: string; email: string; tickets: number; revenue: number }>;
}

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // Get total users
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get total tickets
      const { count: totalTickets } = await supabase
        .from('tickets')
        .select('*', { count: 'exact', head: true });

      // Get tickets data for analysis
      const { data: tickets } = await supabase
        .from('tickets')
        .select('*');

      // Get payments data
      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('status', 'completed');

      // Calculate total revenue
      const totalRevenue = payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0;

      // Calculate average response time (in hours)
      const respondedTickets = tickets?.filter(t => t.responded_at && t.created_at) || [];
      const avgResponseTime = respondedTickets.length > 0
        ? respondedTickets.reduce((sum, ticket) => {
            const created = new Date(ticket.created_at!);
            const responded = new Date(ticket.responded_at!);
            return sum + (responded.getTime() - created.getTime()) / (1000 * 60 * 60);
          }, 0) / respondedTickets.length
        : 0;

      // Group tickets by status
      const ticketsByStatus: Record<string, number> = {};
      tickets?.forEach(ticket => {
        ticketsByStatus[ticket.status] = (ticketsByStatus[ticket.status] || 0) + 1;
      });

      // Group tickets by priority
      const ticketsByPriority: Record<string, number> = {};
      tickets?.forEach(ticket => {
        ticketsByPriority[ticket.priority] = (ticketsByPriority[ticket.priority] || 0) + 1;
      });

      // Revenue by month (last 6 months)
      const revenueByMonth: Array<{ month: string; revenue: number }> = [];
      const now = new Date();
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        const monthRevenue = payments?.filter(payment => {
          const paymentDate = new Date(payment.created_at!);
          return paymentDate.getMonth() === date.getMonth() && 
                 paymentDate.getFullYear() === date.getFullYear();
        }).reduce((sum, payment) => sum + payment.amount, 0) || 0;
        
        revenueByMonth.push({ month: monthName, revenue: monthRevenue });
      }

      // Get top users by tickets and revenue
      const { data: profiles } = await supabase
        .from('profiles')
        .select('*');

      const userStats = await Promise.all(
        (profiles || []).map(async (profile) => {
          const { count: ticketCount } = await supabase
            .from('tickets')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', profile.id);

          const userPayments = payments?.filter(p => p.user_id === profile.id) || [];
          const userRevenue = userPayments.reduce((sum, payment) => sum + payment.amount, 0);

          return {
            id: profile.id,
            name: profile.full_name || 'Unknown',
            email: profile.email || 'Unknown',
            tickets: ticketCount || 0,
            revenue: userRevenue,
          };
        })
      );

      const topUsers = userStats
        .sort((a, b) => (b.tickets + b.revenue / 1000) - (a.tickets + a.revenue / 1000))
        .slice(0, 10);

      setAnalytics({
        totalUsers: totalUsers || 0,
        totalTickets: totalTickets || 0,
        totalRevenue,
        avgResponseTime,
        ticketsByStatus,
        ticketsByPriority,
        revenueByMonth,
        topUsers,
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount / 100);
  };

  const formatHours = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${Math.round(hours)}h`;
    return `${Math.round(hours / 24)}d`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'in_progress': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'resolved': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'closed': return <CheckCircle className="w-4 h-4 text-gray-500" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: 'bg-green-500/20 text-green-400 border-green-500',
      medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500',
      high: 'bg-orange-500/20 text-orange-400 border-orange-500',
      urgent: 'bg-red-500/20 text-red-400 border-red-500',
    };
    
    return (
      <Badge className={colors[priority as keyof typeof colors] || colors.medium}>
        {priority.toUpperCase()}
      </Badge>
    );
  };

  if (loading) {
    return (
      <TerminalWindow title="ANALYTICS_ENGINE.EXE">
        <div className="text-center py-8 text-retro-green/70">
          <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
          Loading analytics data...
        </div>
      </TerminalWindow>
    );
  }

  if (!analytics) {
    return (
      <TerminalWindow title="ANALYTICS_ENGINE.EXE">
        <div className="text-center py-8 text-retro-green/70">
          Failed to load analytics data.
        </div>
      </TerminalWindow>
    );
  }

  return (
    <TerminalWindow title="ANALYTICS_ENGINE.EXE">
      <div className="space-y-6">
        {/* Header with Refresh */}
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-bold text-retro-green">SYSTEM ANALYTICS</h3>
            <p className="text-sm text-retro-green/70">Real-time business intelligence dashboard</p>
          </div>
          <Button
            onClick={loadAnalytics}
            variant="outline"
            className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            REFRESH
          </Button>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-5 h-5" />
              <span className="text-sm text-retro-green/70">TOTAL USERS</span>
            </div>
            <div className="text-2xl font-bold">{analytics.totalUsers}</div>
          </div>
          
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-2">
              <Ticket className="w-5 h-5" />
              <span className="text-sm text-retro-green/70">TOTAL TICKETS</span>
            </div>
            <div className="text-2xl font-bold">{analytics.totalTickets}</div>
          </div>
          
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="w-5 h-5" />
              <span className="text-sm text-retro-green/70">TOTAL REVENUE</span>
            </div>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</div>
          </div>
          
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5" />
              <span className="text-sm text-retro-green/70">AVG RESPONSE</span>
            </div>
            <div className="text-2xl font-bold">{formatHours(analytics.avgResponseTime)}</div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Tickets by Status */}
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              TICKETS BY STATUS
            </h4>
            <div className="space-y-3">
              {Object.entries(analytics.ticketsByStatus).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(status)}
                    <span className="capitalize">{status.replace('_', ' ')}</span>
                  </div>
                  <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Tickets by Priority */}
          <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
            <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              TICKETS BY PRIORITY
            </h4>
            <div className="space-y-3">
              {Object.entries(analytics.ticketsByPriority).map(([priority, count]) => (
                <div key={priority} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getPriorityBadge(priority)}
                  </div>
                  <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                    {count}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Revenue Trend */}
        <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
          <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            REVENUE TREND (LAST 6 MONTHS)
          </h4>
          <div className="grid grid-cols-6 gap-2">
            {analytics.revenueByMonth.map((month, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-retro-green/70 mb-1">{month.month}</div>
                <div className="text-sm font-mono">{formatCurrency(month.revenue)}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Users */}
        <div className="border border-retro-green/50 p-4 bg-retro-terminal/10">
          <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            TOP USERS
          </h4>
          <Table>
            <TableHeader>
              <TableRow className="border-retro-green/50">
                <TableHead className="text-retro-green font-mono">USER</TableHead>
                <TableHead className="text-retro-green font-mono">TICKETS</TableHead>
                <TableHead className="text-retro-green font-mono">REVENUE</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {analytics.topUsers.slice(0, 5).map((user) => (
                <TableRow key={user.id} className="border-retro-green/30">
                  <TableCell className="text-retro-green">
                    <div>
                      <div className="font-semibold">{user.name}</div>
                      <div className="text-xs text-retro-green/70">{user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-retro-green">{user.tickets}</TableCell>
                  <TableCell className="text-retro-green font-mono">
                    {formatCurrency(user.revenue)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </TerminalWindow>
  );
};

export default AnalyticsDashboard;
