import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Monitor, 
  Smartphone, 
  Globe, 
  Info, 
  Copy, 
  Check,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SystemInfo {
  userAgent: string;
  platform: string;
  language: string;
  screenResolution: string;
  viewport: string;
  timezone: string;
  cookiesEnabled: boolean;
  onlineStatus: boolean;
  browserName: string;
  browserVersion: string;
  osName: string;
  deviceType: string;
  timestamp: string;
}

interface SystemInfoCollectorProps {
  onSystemInfoChange: (systemInfo: SystemInfo | null) => void;
  className?: string;
  autoCollect?: boolean;
}

const SystemInfoCollector: React.FC<SystemInfoCollectorProps> = ({
  onSystemInfoChange,
  className,
  autoCollect = true
}) => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [copied, setCopied] = useState(false);
  const [customInfo, setCustomInfo] = useState('');

  useEffect(() => {
    if (autoCollect) {
      collectSystemInfo();
    }
  }, [autoCollect]);

  const detectBrowser = (userAgent: string) => {
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edg')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    return 'Unknown';
  };

  const detectOS = (userAgent: string) => {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  };

  const detectDeviceType = (userAgent: string) => {
    if (/Mobi|Android/i.test(userAgent)) return 'Mobile';
    if (/Tablet|iPad/i.test(userAgent)) return 'Tablet';
    return 'Desktop';
  };

  const getBrowserVersion = (userAgent: string, browserName: string) => {
    const patterns: Record<string, RegExp> = {
      Chrome: /Chrome\/(\d+\.\d+)/,
      Firefox: /Firefox\/(\d+\.\d+)/,
      Safari: /Version\/(\d+\.\d+)/,
      Edge: /Edg\/(\d+\.\d+)/,
      Opera: /Opera\/(\d+\.\d+)/
    };

    const pattern = patterns[browserName];
    if (pattern) {
      const match = userAgent.match(pattern);
      return match ? match[1] : 'Unknown';
    }
    return 'Unknown';
  };

  const collectSystemInfo = () => {
    const userAgent = navigator.userAgent;
    const browserName = detectBrowser(userAgent);
    
    const info: SystemInfo = {
      userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cookiesEnabled: navigator.cookieEnabled,
      onlineStatus: navigator.onLine,
      browserName,
      browserVersion: getBrowserVersion(userAgent, browserName),
      osName: detectOS(userAgent),
      deviceType: detectDeviceType(userAgent),
      timestamp: new Date().toISOString()
    };

    setSystemInfo(info);
    onSystemInfoChange(info);
  };

  const copyToClipboard = async () => {
    if (!systemInfo) return;
    
    const infoText = Object.entries(systemInfo)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
    
    try {
      await navigator.clipboard.writeText(infoText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const getDeviceIcon = () => {
    if (!systemInfo) return <Monitor className="w-5 h-5" />;
    
    switch (systemInfo.deviceType) {
      case 'Mobile':
        return <Smartphone className="w-5 h-5" />;
      case 'Tablet':
        return <Smartphone className="w-5 h-5" />;
      default:
        return <Monitor className="w-5 h-5" />;
    }
  };

  const formatInfoForDisplay = (info: SystemInfo) => {
    return {
      'Browser': `${info.browserName} ${info.browserVersion}`,
      'Operating System': info.osName,
      'Device Type': info.deviceType,
      'Screen Resolution': info.screenResolution,
      'Viewport Size': info.viewport,
      'Language': info.language,
      'Timezone': info.timezone,
      'Online Status': info.onlineStatus ? 'Online' : 'Offline',
      'Cookies Enabled': info.cookiesEnabled ? 'Yes' : 'No'
    };
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Card className="border-retro-green/50 bg-black">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getDeviceIcon()}
              <CardTitle className="text-retro-green font-mono">
                System Information
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-retro-green hover:text-retro-green/80"
              >
                {isExpanded ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={collectSystemInfo}
                className="text-retro-green hover:text-retro-green/80"
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
          <CardDescription className="text-retro-green/70">
            This information helps our support team diagnose issues more effectively
          </CardDescription>
        </CardHeader>

        <CardContent>
          {systemInfo ? (
            <div className="space-y-4">
              {/* Quick Summary */}
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                  {systemInfo.browserName} {systemInfo.browserVersion}
                </Badge>
                <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                  {systemInfo.osName}
                </Badge>
                <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                  {systemInfo.deviceType}
                </Badge>
                <Badge className="bg-retro-green/20 text-retro-green border-retro-green">
                  {systemInfo.screenResolution}
                </Badge>
              </div>

              {/* Detailed View */}
              {isExpanded && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-retro-green font-mono font-bold">Detailed Information</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyToClipboard}
                      className="text-retro-green hover:text-retro-green/80"
                    >
                      {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      {copied ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    {Object.entries(formatInfoForDisplay(systemInfo)).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-retro-green/70">{key}:</span>
                        <span className="text-retro-green font-mono">{value}</span>
                      </div>
                    ))}
                  </div>

                  {/* Custom Additional Info */}
                  <div className="space-y-2">
                    <label className="text-retro-green font-mono text-sm">
                      Additional Information (Optional):
                    </label>
                    <Textarea
                      value={customInfo}
                      onChange={(e) => setCustomInfo(e.target.value)}
                      placeholder="Any additional system details, error messages, or context that might be helpful..."
                      className="bg-black border-retro-green text-retro-green focus:border-retro-green min-h-[80px]"
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4">
              <Button
                onClick={collectSystemInfo}
                className="bg-retro-green text-black hover:bg-retro-green/90 font-mono"
              >
                <Info className="w-4 h-4 mr-2" />
                Collect System Information
              </Button>
              <p className="text-retro-green/60 text-sm mt-2">
                Click to automatically gather your system details
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemInfoCollector;
