import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Clock, Zap } from 'lucide-react';

interface PricingTier {
  id: string;
  name: string;
  price: number;
  description: string;
  features: string[];
  responseTime: string;
  icon: React.ReactNode;
  popular?: boolean;
}

interface PricingTierSelectorProps {
  selectedTier: string;
  onTierSelect: (tier: string) => void;
}

const pricingTiers: PricingTier[] = [
  {
    id: 'standard',
    name: 'Standard Support',
    price: 29.99,
    description: 'Basic support for general inquiries',
    features: [
      'Email support',
      'Standard response time',
      'Basic troubleshooting',
      'Documentation access'
    ],
    responseTime: '24-48 hours',
    icon: <CheckCircle className="w-6 h-6" />
  },
  {
    id: 'priority',
    name: 'Priority Support',
    price: 49.99,
    description: 'Faster response for urgent issues',
    features: [
      'Priority email support',
      'Faster response time',
      'Advanced troubleshooting',
      'Phone support available',
      'Escalation to senior staff'
    ],
    responseTime: '4-12 hours',
    icon: <Clock className="w-6 h-6" />,
    popular: true
  },
  {
    id: 'urgent',
    name: 'Urgent Support',
    price: 99.99,
    description: 'Immediate attention for critical issues',
    features: [
      'Immediate response',
      'Direct phone support',
      'Screen sharing assistance',
      'Dedicated support engineer',
      'Real-time chat support',
      'Emergency escalation'
    ],
    responseTime: '< 1 hour',
    icon: <Zap className="w-6 h-6" />
  }
];

const PricingTierSelector: React.FC<PricingTierSelectorProps> = ({
  selectedTier,
  onTierSelect
}) => {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-bold text-retro-green mb-2">SELECT SUPPORT TIER</h3>
        <p className="text-retro-green/70 text-sm">Choose the level of support that best fits your needs</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {pricingTiers.map((tier) => (
          <Card
            key={tier.id}
            className={`relative cursor-pointer transition-all duration-200 ${
              selectedTier === tier.id
                ? 'border-retro-green bg-retro-green/10 shadow-lg shadow-retro-green/20'
                : 'border-retro-green/50 bg-black hover:border-retro-green hover:bg-retro-green/5'
            }`}
            onClick={() => onTierSelect(tier.id)}
          >
            {tier.popular && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-retro-amber text-black font-bold">MOST POPULAR</Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-2">
              <div className="flex justify-center mb-2 text-retro-green">
                {tier.icon}
              </div>
              <CardTitle className="text-retro-green text-lg">{tier.name}</CardTitle>
              <div className="text-2xl font-bold text-retro-green">
                ${tier.price}
                <span className="text-sm font-normal text-retro-green/70">/ticket</span>
              </div>
              <CardDescription className="text-retro-green/70 text-sm">
                {tier.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="pt-2">
              <div className="space-y-3">
                <div className="text-center">
                  <Badge 
                    variant="outline" 
                    className="border-retro-green text-retro-green bg-transparent"
                  >
                    Response: {tier.responseTime}
                  </Badge>
                </div>
                
                <ul className="space-y-2">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-retro-green/80">
                      <CheckCircle className="w-4 h-4 mr-2 text-retro-green flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
            
            {selectedTier === tier.id && (
              <div className="absolute inset-0 border-2 border-retro-green rounded-lg pointer-events-none">
                <div className="absolute top-2 right-2">
                  <CheckCircle className="w-6 h-6 text-retro-green fill-current" />
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>
      
      <div className="text-center text-xs text-retro-green/60 mt-4">
        <p>All payments are processed securely through Stripe</p>
        <p>Refunds available within 30 days of purchase</p>
      </div>
    </div>
  );
};

export default PricingTierSelector;
