# Enhanced Payment System Setup Guide

This guide covers the setup and configuration of the enhanced payment system for the Retro Help Desk Portal.

## Overview

The enhanced payment system includes:
- **Multiple pricing tiers** (Standard, Priority, Urgent)
- **Secure Stripe webhook handling**
- **Payment refund management**
- **Admin payment dashboard**
- **Payment receipts and analytics**
- **Enhanced security with RLS policies**

## Prerequisites

1. **Stripe Account**: You need a Stripe account with API keys
2. **Supabase Project**: Active Supabase project with database access
3. **Environment Variables**: Properly configured environment variables

## Environment Variables

### Supabase Edge Functions
Add these environment variables to your Supabase project:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_... # Your Stripe secret key
STRIPE_WEBHOOK_SECRET=whsec_... # Webhook endpoint secret

# Supabase Configuration  
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ... # Service role key for server operations
```

### Frontend Environment
Create a `.env.local` file in your project root:

```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=eyJ... # Anonymous key for client operations
```

## Database Setup

### 1. Run the Migration

Apply the enhanced payment system migration:

```sql
-- Run this in your Supabase SQL editor or via CLI
-- File: supabase/migrations/20240101000000_enhance_payment_system.sql
```

This migration will:
- Add new columns to the `payments` table
- Create a `refunds` table
- Set up proper indexes and RLS policies
- Configure triggers for automatic timestamp updates

### 2. Verify Tables

Ensure these tables exist with proper structure:

#### Payments Table
```sql
payments (
  id UUID PRIMARY KEY,
  ticket_id UUID REFERENCES tickets(id),
  user_id UUID NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT DEFAULT 'usd',
  pricing_tier TEXT DEFAULT 'standard',
  status TEXT DEFAULT 'pending',
  stripe_session_id TEXT,
  stripe_customer_id TEXT,
  stripe_payment_intent_id TEXT,
  stripe_charge_id TEXT,
  stripe_refund_id TEXT,
  refund_amount INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

#### Refunds Table
```sql
refunds (
  id UUID PRIMARY KEY,
  payment_id UUID REFERENCES payments(id),
  stripe_refund_id TEXT,
  amount INTEGER NOT NULL,
  reason TEXT,
  status TEXT DEFAULT 'completed',
  processed_by UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

## Stripe Configuration

### 1. Create Webhook Endpoint

In your Stripe Dashboard:
1. Go to **Developers > Webhooks**
2. Click **Add endpoint**
3. Set URL to: `https://your-project.supabase.co/functions/v1/stripe-webhook`
4. Select these events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `charge.dispute.created`

### 2. Configure Webhook Secret

Copy the webhook signing secret and add it to your Supabase environment variables as `STRIPE_WEBHOOK_SECRET`.

### 3. Test Mode vs Live Mode

- **Development**: Use test API keys (sk_test_...)
- **Production**: Use live API keys (sk_live_...)

## Supabase Edge Functions Deployment

Deploy the payment-related functions:

```bash
# Deploy create payment function
supabase functions deploy create-ticket-payment

# Deploy webhook handler
supabase functions deploy stripe-webhook

# Deploy refund function
supabase functions deploy refund-payment

# Deploy payment verification
supabase functions deploy verify-payment
```

## Features Overview

### 1. Pricing Tiers

| Tier | Price | Response Time | Features |
|------|-------|---------------|----------|
| Standard | $29.99 | 24-48 hours | Basic support |
| Priority | $49.99 | 4-12 hours | Priority support + phone |
| Urgent | $99.99 | < 1 hour | Immediate response + dedicated engineer |

### 2. Payment Flow

1. **User creates ticket** → Selects pricing tier
2. **Payment session created** → Stripe Checkout opens
3. **Payment processed** → Webhook updates database
4. **Ticket activated** → Admin can view and respond

### 3. Admin Features

- **Payment Management**: View all payments, process refunds
- **Analytics Dashboard**: Revenue tracking, payment statistics
- **Refund Processing**: Full or partial refunds with reason tracking
- **Receipt Generation**: Downloadable payment receipts

### 4. Security Features

- **Row Level Security**: Users can only access their own data
- **Webhook Verification**: Stripe signature validation
- **Admin-only Actions**: Refunds require admin privileges
- **Audit Trail**: All payment actions are logged

## Testing

### 1. Test Payment Flow

Use Stripe test cards:
- **Success**: `4242 4242 4242 4242`
- **Decline**: `4000 0000 0000 0002`
- **Requires Authentication**: `4000 0025 0000 3155`

### 2. Test Webhooks

Use Stripe CLI to forward webhooks to local development:

```bash
stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook
```

### 3. Test Refunds

1. Create a successful payment
2. Access admin panel at `/admin`
3. Navigate to Payments tab
4. Process a refund for the payment

## Monitoring and Logs

### View Function Logs

```bash
# View webhook logs
supabase functions logs stripe-webhook

# View payment creation logs  
supabase functions logs create-ticket-payment

# View refund logs
supabase functions logs refund-payment
```

### Database Monitoring

Monitor payment status and refund processing through the admin dashboard or direct database queries.

## Troubleshooting

### Common Issues

1. **Webhook not receiving events**
   - Check webhook URL is correct
   - Verify webhook secret matches
   - Check Supabase function logs

2. **Payment not updating ticket status**
   - Verify webhook is processing correctly
   - Check database permissions
   - Review function logs for errors

3. **Refunds not processing**
   - Ensure admin has proper permissions
   - Check Stripe refund limits
   - Verify payment is in 'paid' status

### Support

For additional support:
- Check Supabase function logs
- Review Stripe webhook logs
- Verify database RLS policies
- Test with Stripe test mode first

## Security Considerations

1. **Never expose service role key** in client-side code
2. **Always validate webhook signatures** from Stripe
3. **Use RLS policies** to protect user data
4. **Audit admin actions** for refunds and sensitive operations
5. **Monitor for suspicious payment patterns**

## Production Deployment

Before going live:
1. Switch to Stripe live API keys
2. Update webhook endpoints to production URLs
3. Test payment flow end-to-end
4. Set up monitoring and alerting
5. Configure backup and recovery procedures
