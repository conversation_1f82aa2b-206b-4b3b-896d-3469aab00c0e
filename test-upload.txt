This is a test file for testing the file upload functionality in the ticket system.

The file attachment feature should:
1. Allow users to select files via drag & drop or file picker
2. Validate file types and sizes
3. Upload files to Supabase storage
4. Save attachment metadata to the database
5. Display upload progress and success/error messages
6. Show attached files in the ticket form

This test file should be successfully uploaded and attached to a ticket.
