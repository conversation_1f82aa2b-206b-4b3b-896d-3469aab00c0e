<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>File Upload Test</h1>
    <input type="file" id="fileInput" />
    <button onclick="testUpload()">Test Upload</button>
    <div id="result"></div>

    <script>
        const supabaseUrl = 'https://nujwyiguqgrhrnvxndmd.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im51and5aWd1cWdyaHJudnhuZG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5Mzg0OTgsImV4cCI6MjA2NDUxNDQ5OH0.ojjUr_qYLfPXckk7CZ8PtM8QDdrNkNwgjJ6YTZP1FJw';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = 'Please select a file first';
                return;
            }

            const file = fileInput.files[0];
            console.log('Selected file:', file);

            try {
                // Check if user is authenticated
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                console.log('Current user:', user);
                
                if (authError || !user) {
                    resultDiv.innerHTML = 'Error: User not authenticated. Please log in first.';
                    return;
                }

                // Test file upload
                const fileName = `test-${Date.now()}-${file.name}`;
                const filePath = `ticket-attachments/${user.id}/${fileName}`;
                
                console.log('Uploading to path:', filePath);
                
                const { data, error } = await supabase.storage
                    .from('attachments')
                    .upload(filePath, file, {
                        cacheControl: '3600',
                        upsert: false
                    });

                if (error) {
                    console.error('Upload error:', error);
                    resultDiv.innerHTML = `Upload failed: ${error.message}`;
                    return;
                }

                console.log('Upload successful:', data);
                
                // Get public URL
                const { data: { publicUrl } } = supabase.storage
                    .from('attachments')
                    .getPublicUrl(filePath);

                console.log('Public URL:', publicUrl);
                
                resultDiv.innerHTML = `
                    <p>Upload successful!</p>
                    <p>Path: ${data.path}</p>
                    <p>Public URL: ${publicUrl}</p>
                `;

            } catch (error) {
                console.error('Test error:', error);
                resultDiv.innerHTML = `Test failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
